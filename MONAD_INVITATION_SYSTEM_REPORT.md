# Monad 多链邀请系统技术方案报告

## 执行摘要

本方案设计了一个支持多链部署的去中心化邀请系统，专为 Monad 测试网优化，并预留了对 Base、Ethereum 等 EVM 兼容链的支持。系统采用懒加载上链机制，实现了零门槛用户进入，同时保持了完整的链上可验证性和自动化奖励分配。

## 1. 系统架构

### 1.1 核心组件

#### 智能合约层
- **MonadInvitationSystem.sol**: 核心邀请系统合约
  - 支持原生代币和 ERC20 代币奖励
  - 实现懒加载上链机制
  - 多链配置支持
  - 三级邀请奖励体系（可配置比例）

- **BatchAirdrop.sol**: 批量空投合约
  - 支持大规模代币分发
  - 自动计算和分配邀请奖励
  - Gas 优化的批处理操作

#### 前端集成层
- **verifyInvitation.ts**: 邀请码验证工具
  - 支持客户端本地验证
  - 与合约交互的完整接口
  - TypeScript 类型安全

- **lazyJoinImplementation.ts**: 懒加载实现
  - JWT 令牌管理
  - 链下验证服务
  - 自动上链触发机制

- **multichain-config.ts**: 多链配置管理
  - 链配置中心化管理
  - 自动链切换支持
  - React Hooks 集成

### 1.2 技术特点

1. **零 Gas 进入**: 用户通过邀请码可立即访问应用，无需支付 Gas
2. **懒加载上链**: 仅在用户执行需要链上记录的操作时才触发上链
3. **多链就绪**: 预置主流 EVM 链配置，便于快速扩展
4. **高度优化**: 
   - 加入系统: ~120,000 gas
   - 懒加载加入 + 操作: ~180,000 gas
   - 批量空投: ~50,000 gas/用户

## 2. 用户流程

### 2.1 新用户加入流程

```mermaid
graph LR
    A[收到邀请链接] --> B[连接钱包]
    B --> C[链下验证邀请码]
    C --> D[获得 JWT 访问令牌]
    D --> E[进入应用]
    E --> F{执行操作}
    F -->|需要链上记录| G[自动完成上链注册]
    F -->|仅浏览| H[保持链下状态]
```

### 2.2 具体实现步骤

1. **Alice（根用户）邀请 Bob**
   ```
   邀请链接: https://app.com/join?chain=monad_testnet&inviter=0xAlice&code=0x123...
   ```

2. **Bob 点击链接加入**
   - 前端本地验证邀请码（瞬时完成）
   - 后端确认邀请人资格（1次 RPC 调用）
   - 签发 JWT，Bob 获得应用访问权限
   - **此时 Bob 未支付任何 Gas**

3. **Bob 使用应用**
   - 浏览内容、查看数据等操作正常进行
   - 系统显示状态："已验证，待上链"

4. **Bob 邀请 Charlie（触发上链）**
   - 系统检测到 Bob 未完成链上注册
   - 提示：将同时完成您的注册（预计 Gas: $0.50）
   - 一笔交易完成：注册 + 生成邀请码

5. **后续空投自动分配**
   - Bob 已完成链上注册
   - 未来的空投将自动按照邀请关系分配奖励

## 3. 技术实现细节

### 3.1 邀请码生成算法

```solidity
function generateInviteCode(address inviter, address invitee) public pure returns (bytes32) {
    return keccak256(abi.encodePacked(inviter, invitee));
}
```

- **优势**：
  - 确定性生成，无需存储
  - 无法伪造（需要知道双方地址）
  - 可离线生成和验证
  - Gas 成本极低

### 3.2 懒加载机制

```solidity
modifier autoJoin(address inviter, bytes32 inviteCode) {
    if (!hasJoined[msg.sender] && inviter != address(0) && inviteCode != bytes32(0)) {
        _joinWithInvitation(msg.sender, inviter, inviteCode);
    }
    _;
}
```

任何需要链上权限的操作都可以触发自动注册：
- `generateInviteWithJoin`: 生成邀请码时自动注册
- `claimNativeWithJoin`: 领取原生代币时自动注册
- `claimTokenWithJoin`: 领取 ERC20 时自动注册

### 3.3 奖励分配机制

默认分配比例（可配置）：
- 用户本人: 70%
- 直接邀请人: 20%
- 间接邀请人: 10%

支持为不同代币设置不同的分配比例：
```solidity
system.configureRewardToken(
    tokenAddress,
    userPercent,
    inviterPercent,
    grandInviterPercent
);
```

### 3.4 批量操作优化

批量空投示例：
```solidity
function batchAirdrop(
    address token,
    address[] calldata recipients,
    uint256[] calldata amounts
) external onlyOwner
```

- 自动处理邀请奖励分配
- 支持原生代币和 ERC20
- Gas 优化的循环处理

## 4. 部署指南

### 4.1 环境准备

```bash
# 安装依赖
forge install

# 设置环境变量
export DEPLOYER_PRIVATE_KEY=your_private_key_without_0x
```

### 4.2 部署到 Monad 测试网

```bash
forge script script/DeployInvitation.s.sol:DeployInvitation \
    --rpc-url https://devnet.monad.xyz \
    --broadcast \
    --verify
```

### 4.3 部署到其他链

```bash
# Base Sepolia
forge script script/DeployInvitation.s.sol:DeployInvitation \
    --rpc-url https://sepolia.base.org \
    --broadcast

# Ethereum Sepolia  
forge script script/DeployInvitation.s.sol:DeployInvitation \
    --rpc-url https://sepolia.etherscan.io \
    --broadcast
```

### 4.4 验证合约

```bash
forge verify-contract <CONTRACT_ADDRESS> MonadInvitationSystem \
    --chain-id 41454 \
    --compiler-version v0.8.28
```

## 5. 前端集成

### 5.1 初始化验证器

```typescript
import { InvitationVerifier } from '@/scripts/verifyInvitation';
import { MultiChainInvitationVerifier } from '@/scripts/multichain-config';

// 单链验证
const verifier = new InvitationVerifier(
    'https://devnet.monad.xyz',
    INVITATION_SYSTEM_ADDRESS
);

// 多链支持
const multiVerifier = new MultiChainInvitationVerifier('monad_testnet');
```

### 5.2 验证邀请码

```typescript
// 本地验证（无 RPC 调用）
const isValid = InvitationVerifier.verifyInvitationLocally(
    inviterAddress,
    userAddress,
    inviteCode
);

// 完整验证（包括链上状态）
const status = await verifier.verifyInvitation(
    inviterAddress,
    userAddress,
    inviteCode
);

if (status.canJoin) {
    // 用户可以加入
} else {
    console.error(status.errorMessage);
}
```

### 5.3 懒加载集成

```typescript
import { useLazyJoin } from '@/scripts/lazyJoinImplementation';

function MyComponent() {
    const { joinStatus, joinOffChain, executeWithAutoJoin } = useLazyJoin();
    
    // 链下加入
    const handleJoin = async () => {
        await joinOffChain(inviterAddress, inviteCode);
    };
    
    // 执行需要链上权限的操作
    const handleClaim = async () => {
        await executeWithAutoJoin(async () => {
            return contract.claimTokenWithJoin(
                tokenAddress,
                amount,
                storedInviter,
                storedInviteCode
            );
        });
    };
}
```

## 6. 安全考虑

### 6.1 合约安全
- 使用 OpenZeppelin 标准库
- 防重入保护（通过 SafeERC20）
- 权限管理（Ownable）
- 紧急提取功能

### 6.2 验证安全
- 邀请码基于密码学哈希
- 防止自我邀请
- 防止重复加入
- 邀请人资格验证

### 6.3 前端安全
- JWT 令牌过期机制
- 本地验证 + 链上确认
- 防止中间人攻击

## 7. Gas 成本分析

| 操作 | Gas 消耗 | 成本（@100 gwei） |
|------|---------|------------------|
| 直接加入 | ~120,000 | ~$0.36 |
| 懒加载加入+操作 | ~180,000 | ~$0.54 |
| 生成邀请码 | ~30,000 | ~$0.09 |
| 批量空投（每人） | ~50,000 | ~$0.15 |

*注：成本基于 ETH 价格 $3000 计算*

## 8. 扩展性

### 8.1 功能扩展
- **多级邀请**: 可扩展到 N 级邀请关系
- **动态奖励**: 根据邀请数量调整奖励比例
- **NFT 集成**: 为达到特定邀请数量的用户铸造 NFT
- **时间锁定**: 添加邀请关系的时效性

### 8.2 链扩展
系统已预留以下链的配置：
- Monad Mainnet（待上线）
- Base Mainnet/Testnet
- Ethereum Mainnet/Sepolia
- 其他 EVM 兼容链

### 8.3 性能优化
- 使用事件日志进行链下数据索引
- 批量操作减少交易次数
- 预计算邀请码减少 RPC 调用

## 9. 监控和分析

### 9.1 关键指标
- 总用户数
- 链上/链下用户比例
- 平均邀请深度
- 奖励分配统计

### 9.2 数据导出
系统支持导出邀请网络数据用于分析：
```javascript
// 导出所有用户数据
node scripts/exportInvitationData.js

// Python 分析脚本
python scripts/analyze_invitation_network.py
```

## 10. 总结

本邀请系统成功解决了 Web3 应用的用户获取难题：

**核心优势**：
1. ✅ **零门槛进入**: 用户无需支付 Gas 即可使用应用
2. ✅ **病毒式传播**: 自动化的奖励机制激励用户主动推广
3. ✅ **完全去中心化**: 所有邀请关系永久记录在链上
4. ✅ **多链支持**: 轻松扩展到任何 EVM 兼容链
5. ✅ **极致优化**: Gas 成本降至最低，适合大规模使用

**适用场景**：
- DeFi 协议用户增长
- GameFi 玩家招募
- NFT 项目社区建设
- 空投分发和管理
- 任何需要用户增长的 Web3 应用

通过这套系统，项目方可以快速建立一个透明、公平、高效的用户增长体系，同时为早期用户提供持续的被动收入机会。