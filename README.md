# Monad Invitation System

A clean, minimal invitation system for Monad blockchain applications, extracted from the original 2048 game contracts.

## 📦 Two Versions Available

| Version | Features | Use Case |
|---------|----------|----------|
| **Original** | Immutable, ultra-simple | Maximum simplicity, no upgrade needs |
| **Upgradeable** | Supports upgrades, flexible | Future upgrade capability, evolving business |

## Features

- **Decentralized Invitation System**: On-chain invitation tracking with referral chains
- **Minimal Proxy Pattern**: Gas-efficient user vault deployment using Sol<PERSON>'s Clone library
- **Event-Driven Architecture**: Off-chain indexing with Alchemy API
- **Gas Optimization**: Optimized data types and storage patterns
- **Upgrade Support**: Optional upgradeability for future enhancements

## Architecture

### Version Comparison

| Component | Original Version | Upgradeable Version |
|-----------|------------------|---------------------|
| **Registry** | `InvitationRegistry.sol` (immutable) | `InvitationRegistryUpgradeable.sol` |
| **Vault** | `UserVault.sol` | `UserVaultUpgradeable.sol` |
| **Deployment** | `deploy.ts` | `deployUpgradeable.ts` |
| **Upgrades** | ❌ Not supported | ✅ `UpgradeVaultUpgradeable.s.sol` |

### Key Features

1. **Gas Optimized**: Uses Solady's Clone library for minimal proxy deployment
2. **Referral Tracking**: Complete invitation chain tracking and analytics
3. **Event-Driven**: Off-chain indexing with Alchemy API
4. **Upgrade Support**: Optional upgrade capability for business evolution

## Project Structure

```
├── src/                              # Smart contracts (Forge standard)
│   ├── InvitationRegistry.sol        # Original (immutable)
│   ├── InvitationRegistryUpgradeable.sol  # Upgradeable version
│   ├── UserVault.sol                 # Original vault
│   └── UserVaultUpgradeable.sol      # Upgradeable vault
├── script/                           # Deployment & upgrade scripts
│   └── UpgradeVaultUpgradeable.s.sol # Upgrade management
├── test/                             # Test files
│   └── InvitationRegistry.t.sol      # Universal tests
├── scripts/                          # TypeScript utilities
│   ├── deploy.ts                     # Original deployment
│   ├── deployUpgradeable.ts          # Upgradeable deployment
│   ├── invitation-sdk.ts             # Universal SDK
│   └── alchemy-query-service.ts      # Off-chain queries
└── foundry.toml           # Forge configuration
```

## Version Selection Guide

### Choose Original Version If:
- ✅ You want maximum simplicity and gas optimization
- ✅ You're certain you won't need upgrade functionality
- ✅ You prefer completely immutable contracts
- ✅ You prioritize deployment cost savings

### Choose Upgradeable Version If:
- ✅ You need future upgrade capability
- ✅ Your business logic might evolve
- ✅ You want the ability to fix potential bugs
- ✅ You prioritize flexibility over immutability

## Deployment

The contracts are configured for Monad testnet (Chain ID: 10143).

### Original Version
```bash
# Deploy original (immutable) version
forge script script/Deploy.s.sol --rpc-url https://testnet-rpc.monad.xyz --private-key $PRIVATE_KEY --broadcast

# Or with npm
npm run deploy
```

### Upgradeable Version
```bash
# Deploy upgradeable version
forge script script/DeployUpgradeable.s.sol --rpc-url https://testnet-rpc.monad.xyz --private-key $PRIVATE_KEY --broadcast

# Or with npm
npm run deploy:upgradeable

# Later, upgrade vault implementation
forge script script/UpgradeVaultUpgradeable.s.sol:UpgradeVaultUpgradeable --broadcast --rpc-url $RPC_URL

# Batch upgrade users
forge script script/UpgradeVaultUpgradeable.s.sol:BatchUpgradeUsersUpgradeable --broadcast --rpc-url $RPC_URL
```

## Development

This is a Foundry project. You can find installation instructions for foundry, [here](https://book.getfoundry.sh/getting-started/installation). Clone the repository and run the following commands:

### Install

```shell
$ forge install
```

### Build

```shell
$ forge build
```

### Test

```shell
$ forge test
```

### Format

```shell
$ forge fmt
```

## Frontend Integration

### React Components

```tsx
import { InviteComponent, JoinComponent } from './frontend/InviteComponent';

// For existing users to invite others
<InviteComponent registryAddress="0x..." />

// For new users to join via invitation
<JoinComponent registryAddress="0x..." />
```

### TypeScript SDK

```typescript
import { InvitationSDK } from './scripts/invitation-sdk';

const sdk = new InvitationSDK(registryAddress, provider);

// Verify invitation
const result = await sdk.verifyInvitation(inviterAddress, inviteeAddress);

// Register new user
const { success, vault } = await sdk.register(signer, inviterAddress);

// Get user info
const userInfo = await sdk.getUserInfo(userAddress);
```

## Configuration

Configured for Monad blockchain in `foundry.toml`:
- **RPC**: `https://testnet-rpc.monad.xyz`
- **Chain ID**: `10143`
- **Optimizations**: Enabled with 2M optimizer runs
- **IR**: Enabled for better optimization

## License

MIT
