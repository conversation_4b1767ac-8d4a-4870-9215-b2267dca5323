// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Script.sol";
import {InvitationRegistry} from "../src/InvitationRegistry.sol";

contract DeployInvitation is Script {
    struct ChainConfig {
        uint256 chainId;
        string chainName;
        string rpcUrl;
    }
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("DEPLOYER_PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        // Get chain config
        ChainConfig memory config = getChainConfig();
        
        console.log("========================================");
        console.log("Deploying to:", config.chainName);
        console.log("Chain ID:", config.chainId);
        console.log("Deployer:", deployer);
        console.log("========================================");
        
        vm.startBroadcast(deployerPrivateKey);

        // Deploy InvitationRegistry
        InvitationRegistry invitationRegistry = new InvitationRegistry(deployer);
        console.log("InvitationRegistry deployed at:", address(invitationRegistry));
        console.log("Root user set to:", deployer);

        vm.stopBroadcast();
        
        console.log("\n========== Deployment Summary ==========");
        console.log("Chain:", config.chainName);
        console.log("InvitationRegistry:", address(invitationRegistry));
        console.log("Root User:", deployer);
        console.log("========================================");
        console.log("\nDeployment complete! You can now:");
        console.log("1. Generate invitation codes");
        console.log("2. Build your referral network");
        console.log("3. Start inviting users to join");
    }
    
    function getChainConfig() internal view returns (ChainConfig memory) {
        uint256 chainId = block.chainid;
        
        // Monad Testnet
        if (chainId == 10143) {
            return ChainConfig({
                chainId: 10143,
                chainName: "Monad Testnet",
                rpcUrl: "https://testnet-rpc.monad.xyz"
            });
        }
        // Base
        else if (chainId == 8453) {
            return ChainConfig({
                chainId: 8453,
                chainName: "Base",
                rpcUrl: "https://mainnet.base.org"
            });
        }
        // Base Sepolia
        else if (chainId == 84532) {
            return ChainConfig({
                chainId: 84532,
                chainName: "Base Sepolia",
                rpcUrl: "https://sepolia.base.org"
            });
        }
        // Ethereum Mainnet
        else if (chainId == 1) {
            return ChainConfig({
                chainId: 1,
                chainName: "Ethereum Mainnet",
                rpcUrl: "https://eth.llamarpc.com"
            });
        }
        // Ethereum Sepolia
        else if (chainId == 11155111) {
            return ChainConfig({
                chainId: 11155111,
                chainName: "Ethereum Sepolia",
                rpcUrl: "https://sepolia.etherscan.io"
            });
        }
        // Default/Unknown
        else {
            return ChainConfig({
                chainId: chainId,
                chainName: "Unknown Chain",
                rpcUrl: ""
            });
        }
    }
}