// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "lib/forge-std/src/Script.sol";
import "../src/MatchPurchase.sol";
import "../src/MatchPurchaseUpgradeable.sol";
import "../src/InvitationRegistry.sol";
import "../src/InvitationRegistryUpgradeable.sol";

/**
 * @title DeployMatchPurchase
 * @notice 部署MatchPurchase合约的脚本
 */
contract DeployMatchPurchase is Script {
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        console.log("Deploying contracts with account:", deployer);
        console.log("Account balance:", deployer.balance);
        
        vm.startBroadcast(deployerPrivateKey);
        
        // 获取已部署的Registry合约地址（需要从环境变量或手动设置）
        address registryAddress = vm.envOr("REGISTRY_ADDRESS", address(0));
        address registryUpgradeableAddress = vm.envOr("REGISTRY_UPGRADEABLE_ADDRESS", address(0));
        
        if (registryAddress != address(0)) {
            console.log("Deploying MatchPurchase with Registry:", registryAddress);
            
            // 部署MatchPurchase合约
            MatchPurchase matchPurchase = new MatchPurchase(registryAddress);
            
            console.log("MatchPurchase deployed at:", address(matchPurchase));
            console.log("Registry address:", address(matchPurchase.registry()));
            console.log("Base price:", matchPurchase.BASE_PRICE());
            console.log("Registration fee:", matchPurchase.registrationFee());
        }
        
        if (registryUpgradeableAddress != address(0)) {
            console.log("Deploying MatchPurchaseUpgradeable with Registry:", registryUpgradeableAddress);
            
            // 部署MatchPurchaseUpgradeable合约
            MatchPurchaseUpgradeable matchPurchaseUpgradeable = new MatchPurchaseUpgradeable(registryUpgradeableAddress);
            
            console.log("MatchPurchaseUpgradeable deployed at:", address(matchPurchaseUpgradeable));
            console.log("Registry address:", address(matchPurchaseUpgradeable.registry()));
            console.log("Base price:", matchPurchaseUpgradeable.BASE_PRICE());
            console.log("Registration fee:", matchPurchaseUpgradeable.registrationFee());
        }
        
        if (registryAddress == address(0) && registryUpgradeableAddress == address(0)) {
            console.log("No registry addresses provided. Please set REGISTRY_ADDRESS and/or REGISTRY_UPGRADEABLE_ADDRESS environment variables.");
        }
        
        vm.stopBroadcast();
    }
    
    /**
     * @notice 部署完整系统（Registry + MatchPurchase）
     */
    function deployFullSystem() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        address rootUser = vm.envOr("ROOT_USER", deployer);
        
        console.log("Deploying full system with account:", deployer);
        console.log("Root user:", rootUser);
        
        vm.startBroadcast(deployerPrivateKey);
        
        // 部署Registry合约
        InvitationRegistry registry = new InvitationRegistry(rootUser);
        console.log("InvitationRegistry deployed at:", address(registry));
        
        // 部署MatchPurchase合约
        MatchPurchase matchPurchase = new MatchPurchase(address(registry));
        console.log("MatchPurchase deployed at:", address(matchPurchase));
        
        // 部署可升级版本
        InvitationRegistryUpgradeable registryUpgradeable = new InvitationRegistryUpgradeable(rootUser);
        console.log("InvitationRegistryUpgradeable deployed at:", address(registryUpgradeable));
        
        MatchPurchaseUpgradeable matchPurchaseUpgradeable = new MatchPurchaseUpgradeable(address(registryUpgradeable));
        console.log("MatchPurchaseUpgradeable deployed at:", address(matchPurchaseUpgradeable));
        
        // 输出部署信息
        console.log("\n=== Deployment Summary ===");
        console.log("Non-upgradeable version:");
        console.log("  Registry:", address(registry));
        console.log("  MatchPurchase:", address(matchPurchase));
        console.log("\nUpgradeable version:");
        console.log("  Registry:", address(registryUpgradeable));
        console.log("  MatchPurchase:", address(matchPurchaseUpgradeable));
        
        vm.stopBroadcast();
    }
}
