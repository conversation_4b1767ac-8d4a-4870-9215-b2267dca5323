// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Script.sol";
import "../src/InvitationRegistryUpgradeable.sol";
import "../src/UserVaultUpgradeable.sol";

/**
 * @title UpgradeVaultUpgradeable
 * @notice 升级UserVaultUpgradeable实现的部署脚本 - 可升级版本
 */
contract UpgradeVaultUpgradeable is Script {
    // 配置参数
    address constant REGISTRY_ADDRESS = 0x1234567890123456789012345678901234567890; // 替换为实际地址

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("Deployer:", deployer);
        console.log("Registry:", REGISTRY_ADDRESS);

        vm.startBroadcast(deployerPrivateKey);

        // 1. 部署新的UserVaultUpgradeable实现
        console.log("\n=== 部署UserVaultUpgradeable实现 ===");
        UserVaultUpgradeable vaultV2Implementation = new UserVaultUpgradeable();
        console.log("UserVaultUpgradeable Implementation deployed at:", address(vaultV2Implementation));

        // 2. 获取Registry合约
        InvitationRegistryUpgradeable registry = InvitationRegistryUpgradeable(REGISTRY_ADDRESS);

        // 3. 检查当前状态
        console.log("\n=== 升级前状态 ===");
        address currentImplementation = registry.vaultImplementation();
        uint32 totalUsers = registry.totalUsers();

        console.log("Current Implementation:", currentImplementation);
        console.log("Total Users:", totalUsers);

        // 4. 升级Vault实现
        console.log("\n=== 执行升级 ===");
        registry.upgradeVaultImplementation(address(vaultV2Implementation));

        // 5. 验证升级结果
        console.log("\n=== 升级后状态 ===");
        address newImplementation = registry.vaultImplementation();

        console.log("New Implementation:", newImplementation);

        require(newImplementation == address(vaultV2Implementation), "Implementation not updated");

        console.log("\n✅ 升级成功完成！");
        console.log("📝 新用户将自动使用新实现");
        console.log("📝 管理员可以调用 batchUpgradeVaults() 批量升级老用户");

        vm.stopBroadcast();

        // 6. 生成升级报告
        _generateUpgradeReport(
            currentImplementation,
            address(vaultV2Implementation),
            totalUsers
        );
    }
    
    function _generateUpgradeReport(
        address oldImpl,
        address newImpl,
        uint32 totalUsers
    ) internal view {
        console.log("\n==================================================");
        console.log("           UPGRADE REPORT");
        console.log("==================================================");
        console.log("Upgrade Time:", block.timestamp);
        console.log("Block Number:", block.number);
        console.log("");
        console.log("Old Implementation:", oldImpl);
        console.log("New Implementation:", newImpl);
        console.log("Total Users Affected:", totalUsers);
        console.log("");
        console.log("Next Steps:");
        console.log("1. Monitor upgrade events");
        console.log("2. Run batchUpgradeVaults() to upgrade existing users");
        console.log("3. Update frontend to support new features");
        console.log("==================================================");
    }
}

/**
 * @title BatchUpgradeUsersUpgradeable
 * @notice 批量升级用户Vault的脚本 - 可升级版本
 */
contract BatchUpgradeUsersUpgradeable is Script {
    address constant REGISTRY_ADDRESS = 0x1234567890123456789012345678901234567890;

    // 需要升级的用户地址列表
    address[] public usersToUpgrade = [
        0x1111111111111111111111111111111111111111,
        0x2222222222222222222222222222222222222222,
        0x3333333333333333333333333333333333333333
        // 添加更多用户地址...
    ];

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        console.log("Batch upgrading", usersToUpgrade.length, "users...");

        vm.startBroadcast(deployerPrivateKey);

        InvitationRegistryUpgradeable registry = InvitationRegistryUpgradeable(REGISTRY_ADDRESS);

        // 直接批量升级所有用户
        registry.batchUpgradeVaults(usersToUpgrade);

        vm.stopBroadcast();

        console.log("\n✅ 批量升级完成！");
        console.log("Total users upgraded:", usersToUpgrade.length);
    }
}


