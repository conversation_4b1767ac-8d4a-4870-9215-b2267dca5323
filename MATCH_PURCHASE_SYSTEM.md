# Match Purchase System

## 概述

为你的2048游戏创建了一个购买match次数的合约系统，支持MON代币支付，集成了现有的邀请系统。

## 合约文件

### 主要合约
- `src/MatchPurchase.sol` - 不可升级版本
- `src/MatchPurchaseUpgradeable.sol` - 可升级版本
- `script/DeployMatchPurchase.s.sol` - 部署脚本
- `test/MatchPurchase.t.sol` - 测试文件

## 核心功能

### 1. 价格机制
- **基础价格**: 0.08 MON (第一次购买)
- **递增规则**: 每次购买价格为前一次的108%
- **每日重置**: 每天重新从基础价格开始计算
- **注册费用**: 0.01 MON (未注册用户需额外支付)

### 2. 购买方式

#### 已注册用户购买
```solidity
// 购买1次match
matchPurchase.purchaseMatches{value: 0.08 ether}(1);

// 购买多次match (价格会递增)
uint256 totalCost = matchPurchase.calculateTotalCost(userAddress, 3);
matchPurchase.purchaseMatches{value: totalCost}(3);
```

#### 未注册用户一键注册+购买
```solidity
// 注册并购买1次match
uint256 totalCost = 0.08 ether + 0.01 ether; // 购买费用 + 注册费用
matchPurchase.registerAndPurchase{value: totalCost}(inviterAddress, 1);
```

### 3. 时间计算逻辑
与你现有的Vault合约保持一致，使用相同的时间计算方法：
```solidity
// 基于时间戳差值判断是否为新的一天
if (currentTime - lastPurchaseTimestamp >= 1 days) {
    // 新的一天，重置购买次数
    dailyPurchases = count;
} else {
    // 同一天内，累加购买次数
    dailyPurchases += count;
}
```

## 主要函数

### 查询函数
```solidity
// 计算购买指定次数的总费用
function calculateTotalCost(address user, uint256 count) public view returns (uint256);

// 获取用户当日购买次数
function getUserDailyPurchases(address user) external view returns (uint256);

// 获取下一次购买的价格
function getNextPurchasePrice(address user) external view returns (uint256);

// 获取合约余额
function getContractBalance() external view returns (uint256);
```

### 管理员函数
```solidity
// 提取合约中的MON (仅owner)
function withdrawFunds() external onlyOwner;

// 更新注册费用 (仅owner)
function updateRegistrationFee(uint256 newFee) external onlyOwner;
```

## 事件

### MatchPurchased
用户购买match次数时触发
```solidity
event MatchPurchased(
    address indexed user,      // 用户地址
    uint256 indexed day,       // 购买日期
    uint256 purchaseCount,     // 购买次数
    uint256 totalCost,         // 总费用
    uint256 timestamp          // 时间戳
);
```

### UserRegisteredAndPurchased
用户注册并购买时触发
```solidity
event UserRegisteredAndPurchased(
    address indexed user,      // 用户地址
    address indexed inviter,   // 邀请人地址
    address indexed vault,     // 用户vault地址
    uint256 purchaseCount,     // 购买次数
    uint256 totalCost,         // 总费用(包含注册费)
    uint256 timestamp          // 时间戳
);
```

## 价格计算示例

假设用户在同一天内购买：
- 第1次: 0.08 MON
- 第2次: 0.08 × 1.08 = 0.0864 MON
- 第3次: 0.0864 × 1.08 = 0.093312 MON
- 第4次: 0.093312 × 1.08 = 0.10077696 MON

一次性购买4次的总费用: 0.08 + 0.0864 + 0.093312 + 0.10077696 = 0.36048896 MON

## 安全特性

1. **所有者权限**: 只有合约创建者能提取资金
2. **支付验证**: 严格验证支付金额，不足会回滚
3. **重复注册保护**: 防止已注册用户重复注册
4. **时间一致性**: 与Vault合约使用相同的时间计算逻辑
5. **多余退款**: 自动退还多付的MON

## 部署说明

### 部署命令
```bash
# 部署完整系统 (Registry + MatchPurchase)
forge script script/DeployMatchPurchase.s.sol:DeployMatchPurchase --sig "deployFullSystem()" --rpc-url https://testnet-rpc.monad.xyz --private-key $PRIVATE_KEY --broadcast

# 或者单独部署MatchPurchase (需要先设置环境变量)
export REGISTRY_ADDRESS=0x... # 你的Registry合约地址
forge script script/DeployMatchPurchase.s.sol --rpc-url https://testnet-rpc.monad.xyz --private-key $PRIVATE_KEY --broadcast
```

### 环境变量
```bash
export PRIVATE_KEY=your_private_key
export ROOT_USER=0x... # 根用户地址，默认为部署者地址
export REGISTRY_ADDRESS=0x... # 现有Registry合约地址(可选)
export REGISTRY_UPGRADEABLE_ADDRESS=0x... # 现有可升级Registry合约地址(可选)
```

## 链下查询

你可以通过Alchemy API查询购买事件：
```javascript
// 查询用户购买记录
const filter = {
    address: matchPurchaseAddress,
    topics: [
        ethers.utils.id("MatchPurchased(address,uint256,uint256,uint256,uint256)"),
        ethers.utils.hexZeroPad(userAddress, 32)
    ]
};

const logs = await provider.getLogs({
    ...filter,
    fromBlock: startBlock,
    toBlock: 'latest'
});
```

## 测试

运行测试验证功能：
```bash
forge test --match-contract MatchPurchaseTest -vv
```

测试覆盖：
- ✅ 基础功能测试
- ✅ 价格计算测试
- ✅ 每日重置测试
- ✅ 注册+购买测试
- ✅ 错误情况测试
- ✅ 管理员功能测试
- ✅ 事件测试

## 注意事项

1. **Monad配置**: 合约已配置为Monad测试网 (Chain ID: 10143)
2. **时间同步**: 使用与Vault相同的时间计算逻辑确保一致性
3. **Gas优化**: 使用uint32存储时间戳，优化gas消耗
4. **版本选择**: 根据是否需要升级功能选择对应版本
5. **安全审计**: 建议在主网部署前进行安全审计

## 后续集成

1. **前端集成**: 使用事件查询用户购买历史
2. **后端同步**: 监听事件更新数据库
3. **分析统计**: 基于事件数据进行用户行为分析
4. **价格调整**: 根据需要调整基础价格和递增比例
