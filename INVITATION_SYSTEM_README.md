# Monad 多链邀请系统

## 项目结构

```
invitation-system/
├── src/                            # 智能合约
│   ├── MonadInvitationSystem.sol   # 核心邀请系统合约
│   └── BatchAirdrop.sol            # 批量空投合约
│
├── scripts/                        # 前端集成脚本
│   ├── verifyInvitation.ts         # 邀请码验证工具
│   ├── gaslessInvitation.ts        # 无 Gas 方案（可选）
│   └── lazyJoinImplementation.ts   # 懒加载实现
│
├── script/                         # 部署脚本
│   ├── DeployInvitation.s.sol      # 合约部署脚本
│   └── multichain-config.ts        # 多链配置
│
├── test/                           # 测试文件
│   └── MonadInvitationSystemTest.t.sol
│
└── docs/
    └── MONAD_INVITATION_SYSTEM_REPORT.md  # 详细技术方案
```

## 快速开始

### 1. 安装依赖

```bash
forge install
npm install ethers
```

### 2. 配置环境变量

创建 `.env` 文件：
```env
DEPLOYER_PRIVATE_KEY=your_private_key_without_0x
NEXT_PUBLIC_INVITATION_SYSTEM_ADDRESS=0x...
NEXT_PUBLIC_RPC_URL=https://devnet.monad.xyz
```

### 3. 部署合约

```bash
# 部署到 Monad 测试网
forge script script/DeployInvitation.s.sol:DeployInvitation \
    --rpc-url https://devnet.monad.xyz \
    --broadcast
```

### 4. 前端集成

```typescript
import { InvitationVerifier } from './scripts/verifyInvitation';

// 初始化验证器
const verifier = new InvitationVerifier();

// 验证邀请码
const status = await verifier.verifyInvitation(
    inviterAddress,
    userAddress,
    inviteCode
);

if (status.canJoin) {
    // 用户可以加入
}
```

## 核心功能

### 1. 零 Gas 进入
- 用户通过邀请码验证后立即获得应用访问权限
- 仅在执行链上操作时才需要支付 Gas

### 2. 懒加载上链
```solidity
// 用户在领取奖励时自动完成注册
function claimTokenWithJoin(
    address token,
    uint256 amount,
    address inviter,
    bytes32 inviteCode
) external autoJoin(inviter, inviteCode)
```

### 3. 多链支持
- Monad Testnet (Chain ID: 41454)
- Base (Chain ID: 8453)
- Ethereum (Chain ID: 1)
- 轻松扩展到其他 EVM 链

### 4. 自动奖励分配
- 用户: 70%
- 直接邀请人: 20%
- 间接邀请人: 10%
- 比例可按代币自定义配置

## 使用示例

### 生成邀请码
```typescript
// 本地生成（无需 RPC）
const inviteCode = InvitationVerifier.generateInviteCodeLocally(
    myAddress,
    friendAddress
);

// 生成分享链接
const shareUrl = `https://app.com/join?chain=monad_testnet&inviter=${myAddress}&code=${inviteCode}`;
```

### 验证并加入
```typescript
// 1. 链下验证
const isValid = await verifyOffChain(inviterAddress, userAddress, inviteCode);

// 2. 获得访问权限（JWT）
if (isValid) {
    const token = await getAccessToken();
    // 用户可以开始使用应用
}

// 3. 需要时自动上链
await contract.claimTokenWithJoin(
    tokenAddress,
    amount,
    inviterAddress,
    inviteCode
);
```

### 批量空投
```typescript
// 自动处理邀请奖励
await batchAirdrop.batchAirdrop(
    tokenAddress,
    recipients,
    amounts,
    true // includeReferralRewards
);
```

## Gas 费用

| 操作 | Gas 消耗 | 
|------|---------|
| 加入系统 | ~120,000 |
| 懒加载加入 + 领取 | ~180,000 |
| 批量空投（每用户） | ~50,000 |

## 联系方式

如有问题或建议，请提交 Issue 或 PR。