// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "lib/forge-std/src/Test.sol";
import "../src/MatchPurchase.sol";
import "../src/MatchPurchaseUpgradeable.sol";
import "../src/InvitationRegistry.sol";
import "../src/InvitationRegistryUpgradeable.sol";

contract MatchPurchaseTest is Test {
    MatchPurchase public matchPurchase;
    MatchPurchaseUpgradeable public matchPurchaseUpgradeable;
    InvitationRegistry public registry;
    InvitationRegistryUpgradeable public registryUpgradeable;
    
    address public owner;
    address public rootUser;
    address public user1;
    address public user2;
    address public user3;
    
    uint256 public constant BASE_PRICE = 0.08 ether;
    uint256 public constant REGISTRATION_FEE = 0.01 ether;
    
    function setUp() public {
        owner = address(this);
        rootUser = makeAddr("rootUser");
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");
        user3 = makeAddr("user3");
        
        // 给测试用户一些MON
        vm.deal(user1, 10 ether);
        vm.deal(user2, 10 ether);
        vm.deal(user3, 10 ether);
        
        // 部署Registry合约
        registry = new InvitationRegistry(rootUser);
        registryUpgradeable = new InvitationRegistryUpgradeable(rootUser);
        
        // 部署MatchPurchase合约
        matchPurchase = new MatchPurchase(address(registry));
        matchPurchaseUpgradeable = new MatchPurchaseUpgradeable(address(registryUpgradeable));
    }
    
    // ========== 基础功能测试 ==========
    
    function testDeployment() public {
        assertEq(address(matchPurchase.registry()), address(registry));
        assertEq(matchPurchase.BASE_PRICE(), BASE_PRICE);
        assertEq(matchPurchase.registrationFee(), REGISTRATION_FEE);
        assertEq(matchPurchase.owner(), owner);
    }
    
    function testCalculateTotalCost() public {
        // 第一次购买：0.08 MON
        uint256 cost1 = matchPurchase.calculateTotalCost(user1, 1);
        assertEq(cost1, BASE_PRICE);
        
        // 购买2次：0.08 + 0.08*1.08 = 0.08 + 0.0864 = 0.1664 MON
        uint256 cost2 = matchPurchase.calculateTotalCost(user1, 2);
        assertEq(cost2, BASE_PRICE + (BASE_PRICE * 108 / 100));
        
        // 购买3次：0.08 + 0.0864 + 0.093312 = 0.259712 MON
        uint256 cost3 = matchPurchase.calculateTotalCost(user1, 3);
        uint256 expectedCost3 = BASE_PRICE + 
                               (BASE_PRICE * 108 / 100) + 
                               (BASE_PRICE * 108 * 108 / 10000);
        assertEq(cost3, expectedCost3);
    }
    
    function testGetNextPurchasePrice() public {
        // 初始价格
        uint256 price1 = matchPurchase.getNextPurchasePrice(user1);
        assertEq(price1, BASE_PRICE);
        
        // 注册用户并购买1次
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);
        
        // 下一次价格应该是 0.08 * 1.08
        uint256 price2 = matchPurchase.getNextPurchasePrice(user1);
        assertEq(price2, BASE_PRICE * 108 / 100);
    }
    
    // ========== 购买功能测试 ==========
    
    function testPurchaseMatchesRegisteredUser() public {
        // 先注册用户
        vm.prank(user1);
        registry.register(rootUser);
        
        uint256 initialBalance = address(matchPurchase).balance;
        
        // 购买1次match
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);
        
        // 检查购买记录
        assertEq(matchPurchase.getUserDailyPurchases(user1), 1);
        assertEq(address(matchPurchase).balance, initialBalance + BASE_PRICE);
        
        // 购买第2次，价格应该更高
        uint256 secondPrice = BASE_PRICE * 108 / 100;
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: secondPrice}(1);
        
        assertEq(matchPurchase.getUserDailyPurchases(user1), 2);
    }
    
    function testPurchaseMatchesUnregisteredUser() public {
        uint256 initialBalance = address(matchPurchase).balance;
        uint256 totalCost = BASE_PRICE + REGISTRATION_FEE;
        
        // 未注册用户购买match
        vm.prank(user1);
        matchPurchase.registerAndPurchase{value: totalCost}(rootUser, 1);
        
        // 检查用户已注册
        assertTrue(registry.isRegistered(user1));
        
        // 检查购买记录
        assertEq(matchPurchase.getUserDailyPurchases(user1), 1);
        assertEq(address(matchPurchase).balance, initialBalance + totalCost);
    }
    
    function testPurchaseMultipleMatches() public {
        // 注册用户
        vm.prank(user1);
        registry.register(rootUser);
        
        // 购买3次match
        uint256 totalCost = matchPurchase.calculateTotalCost(user1, 3);
        
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: totalCost}(3);
        
        assertEq(matchPurchase.getUserDailyPurchases(user1), 3);
    }
    
    // ========== 每日重置测试 ==========
    
    function testDailyReset() public {
        // 注册用户
        vm.prank(user1);
        registry.register(rootUser);

        // 第一天购买2次
        uint256 cost1 = matchPurchase.calculateTotalCost(user1, 2);
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: cost1}(2);

        assertEq(matchPurchase.getUserDailyPurchases(user1), 2);

        // 模拟第二天（超过24小时）
        vm.warp(block.timestamp + 1 days + 1);

        // 第二天购买应该重新从基础价格开始
        uint256 nextPrice = matchPurchase.getNextPurchasePrice(user1);
        assertEq(nextPrice, BASE_PRICE);

        // 检查每日购买次数已重置
        assertEq(matchPurchase.getUserDailyPurchases(user1), 0);

        vm.prank(user1);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);

        assertEq(matchPurchase.getUserDailyPurchases(user1), 1);
    }
    
    // ========== 错误情况测试 ==========
    
    function testRevertInsufficientPayment() public {
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user1);
        vm.expectRevert(MatchPurchase.InsufficientPayment.selector);
        matchPurchase.purchaseMatches{value: BASE_PRICE - 1}(1);
    }
    
    function testRevertInvalidPurchaseCount() public {
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user1);
        vm.expectRevert(MatchPurchase.InvalidPurchaseCount.selector);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(0);
    }
    
    function testRevertUnregisteredUser() public {
        vm.prank(user1);
        vm.expectRevert("User not registered");
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);
    }
    
    function testRevertAlreadyRegistered() public {
        // 先注册用户
        vm.prank(user1);
        registry.register(rootUser);
        
        // 尝试再次注册并购买
        vm.prank(user1);
        vm.expectRevert("User already registered");
        matchPurchase.registerAndPurchase{value: BASE_PRICE + REGISTRATION_FEE}(rootUser, 1);
    }
    
    // ========== 管理员功能测试 ==========
    
    function testWithdrawFunds() public {
        // 用户购买产生收入
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);
        
        uint256 contractBalance = address(matchPurchase).balance;
        uint256 ownerBalanceBefore = owner.balance;
        
        // 提取资金
        matchPurchase.withdrawFunds();
        
        assertEq(address(matchPurchase).balance, 0);
        assertEq(owner.balance, ownerBalanceBefore + contractBalance);
    }
    
    function testUpdateRegistrationFee() public {
        uint256 newFee = 0.02 ether;
        
        matchPurchase.updateRegistrationFee(newFee);
        assertEq(matchPurchase.registrationFee(), newFee);
    }
    
    function testRevertInvalidRegistrationFee() public {
        vm.expectRevert(MatchPurchase.InvalidRegistrationFee.selector);
        matchPurchase.updateRegistrationFee(2 ether); // 超过1 ether限制
    }
    
    // ========== 事件测试 ==========
    
    function testMatchPurchasedEvent() public {
        vm.prank(user1);
        registry.register(rootUser);
        
        uint256 currentDay = matchPurchase.getCurrentDay();
        
        vm.expectEmit(true, true, false, true);
        emit MatchPurchased(user1, currentDay, 1, BASE_PRICE, block.timestamp);
        
        vm.prank(user1);
        matchPurchase.purchaseMatches{value: BASE_PRICE}(1);
    }
    
    function testUserRegisteredAndPurchasedEvent() public {
        uint256 currentDay = matchPurchase.getCurrentDay();
        uint256 totalCost = BASE_PRICE + REGISTRATION_FEE;
        
        vm.expectEmit(true, true, true, true);
        emit UserRegisteredAndPurchased(user1, rootUser, address(0), 1, totalCost, block.timestamp);
        
        vm.prank(user1);
        matchPurchase.registerAndPurchase{value: totalCost}(rootUser, 1);
    }
    
    // ========== 事件定义 ==========
    
    event MatchPurchased(
        address indexed user,
        uint256 indexed day,
        uint256 purchaseCount,
        uint256 totalCost,
        uint256 timestamp
    );
    
    event UserRegisteredAndPurchased(
        address indexed user,
        address indexed inviter,
        address indexed vault,
        uint256 purchaseCount,
        uint256 totalCost,
        uint256 timestamp
    );
}
