// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "../src/InvitationRegistry.sol";
import "../src/InvitationRegistryUpgradeable.sol";
import "../src/UserVault.sol";
import "../src/UserVaultUpgradeable.sol";

contract UpgradeFlowTest is Test {
    InvitationRegistry public originalRegistry;
    InvitationRegistryUpgradeable public upgradeableRegistry;
    UserVault public originalVaultImpl;
    UserVaultUpgradeable public upgradeableVaultImpl;
    
    address public rootUser;
    address public user1;
    address public user2;
    address public user3;
    address public admin;

    function setUp() public {
        rootUser = makeAddr("rootUser");
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");
        user3 = makeAddr("user3");
        admin = makeAddr("admin");
        
        // Deploy original system
        originalRegistry = new InvitationRegistry(rootUser);
        originalVaultImpl = new UserVault(address(originalRegistry));

        // Deploy upgradeable system
        upgradeableRegistry = new InvitationRegistryUpgradeable(rootUser);
        upgradeableVaultImpl = new UserVaultUpgradeable();
        
        // Set admin as owner of upgradeable registry
        vm.prank(upgradeableRegistry.owner());
        upgradeableRegistry.transferOwnership(admin);
    }

    function testUpgradeVaultImplementation() public {
        // Deploy new vault implementation
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        
        // Only admin can upgrade
        vm.prank(user1);
        vm.expectRevert();
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        // Admin can upgrade
        vm.prank(admin);
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        assertEq(upgradeableRegistry.vaultImplementation(), address(newVaultImpl));
    }

    function testCompleteUpgradeFlow() public {
        // Step 1: Register users in upgradeable system
        vm.prank(user1);
        address vault1 = upgradeableRegistry.register(rootUser);
        
        vm.prank(user2);
        address vault2 = upgradeableRegistry.register(user1);
        
        vm.prank(user3);
        address vault3 = upgradeableRegistry.register(user2);
        
        // Verify initial state
        UserVaultUpgradeable vaultContract1 = UserVaultUpgradeable(vault1);
        assertEq(vaultContract1.totalInvites(), 1); // user1 only directly invited user2
        assertEq(vaultContract1.owner(), user1);
        assertEq(vaultContract1.inviter(), rootUser);
        
        // Step 2: Deploy new vault implementation
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        
        // Step 3: Update registry with new implementation
        vm.prank(admin);
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        // Step 4: Batch upgrade existing vaults
        address[] memory usersToUpgrade = new address[](3);
        usersToUpgrade[0] = user1;
        usersToUpgrade[1] = user2;
        usersToUpgrade[2] = user3;
        
        vm.prank(admin);
        upgradeableRegistry.batchUpgradeVaults(usersToUpgrade);
        
        // Step 5: Verify data migration
        address newVault1 = upgradeableRegistry.userVaults(user1);
        assertNotEq(newVault1, vault1); // Should be a new vault address
        
        UserVaultUpgradeable newVaultContract1 = UserVaultUpgradeable(newVault1);
        assertEq(newVaultContract1.owner(), user1);
        assertEq(newVaultContract1.inviter(), rootUser);
        // Note: totalInvites might be different due to migration logic
        assertTrue(newVaultContract1.initialized());
    }

    function testUserInitiatedUpgrade() public {
        // Register user in upgradeable system
        vm.prank(user1);
        address oldVault = upgradeableRegistry.register(rootUser);
        
        // Deploy new vault implementation
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        
        // Admin upgrades implementation
        vm.prank(admin);
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        // User can trigger their own upgrade by re-registering (if supported)
        // This would depend on the specific upgrade mechanism implemented
        // For now, we test that the new implementation is used for new registrations
        
        vm.prank(user2);
        address newVault = upgradeableRegistry.register(rootUser);
        
        // New vault should use the new implementation
        UserVaultUpgradeable newVaultContract = UserVaultUpgradeable(newVault);
        assertTrue(newVaultContract.initialized());
        assertEq(newVaultContract.owner(), user2);
    }

    function testUpgradeWithDataValidation() public {
        // Register users and create some invitation data
        vm.prank(user1);
        address vault1 = upgradeableRegistry.register(rootUser);
        
        vm.prank(user2);
        upgradeableRegistry.register(user1);
        
        vm.prank(user3);
        upgradeableRegistry.register(user1);
        
        UserVaultUpgradeable vaultContract1 = UserVaultUpgradeable(vault1);
        uint32 originalTotalInvites = vaultContract1.totalInvites();
        uint32 originalDailyInvites = vaultContract1.dailyInvites();
        uint32 originalLastInviteTimestamp = vaultContract1.lastInviteTimestamp();
        
        // Deploy and set new implementation
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        vm.prank(admin);
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        // Upgrade user1's vault
        address[] memory usersToUpgrade = new address[](1);
        usersToUpgrade[0] = user1;
        
        vm.prank(admin);
        upgradeableRegistry.batchUpgradeVaults(usersToUpgrade);
        
        // Validate data migration
        address newVault1 = upgradeableRegistry.userVaults(user1);
        UserVaultUpgradeable newVaultContract1 = UserVaultUpgradeable(newVault1);
        
        assertEq(newVaultContract1.owner(), user1);
        assertEq(newVaultContract1.inviter(), rootUser);
        assertTrue(newVaultContract1.initialized());
        
        // Data validation would depend on the specific migration logic
        // This is a placeholder for comprehensive data validation
    }

    function testUpgradePermissions() public {
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        
        // Non-admin cannot upgrade implementation
        vm.prank(user1);
        vm.expectRevert();
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        // Non-admin cannot batch upgrade vaults
        address[] memory users = new address[](1);
        users[0] = user1;
        
        vm.prank(user1);
        vm.expectRevert();
        upgradeableRegistry.batchUpgradeVaults(users);
        
        // Admin can perform upgrades
        vm.prank(admin);
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));
        
        vm.prank(admin);
        upgradeableRegistry.batchUpgradeVaults(users); // Should not revert
    }

    function testInvalidUpgradeImplementation() public {
        // Try to upgrade to zero address
        vm.prank(admin);
        vm.expectRevert();
        upgradeableRegistry.upgradeVaultImplementation(address(0));
        
        // Try to upgrade to non-contract address
        vm.prank(admin);
        vm.expectRevert();
        upgradeableRegistry.upgradeVaultImplementation(user1);
    }

    function testUpgradeEvents() public {
        UserVaultUpgradeable newVaultImpl = new UserVaultUpgradeable();
        address oldImpl = upgradeableRegistry.vaultImplementation();

        // Test VaultImplementationUpgraded event
        vm.prank(admin);
        vm.expectEmit(true, true, false, true);
        emit InvitationRegistryUpgradeable.VaultImplementationUpgraded(
            oldImpl,
            address(newVaultImpl)
        );
        upgradeableRegistry.upgradeVaultImplementation(address(newVaultImpl));

        // Register a user to have a vault to upgrade
        vm.prank(user1);
        upgradeableRegistry.register(rootUser);

        address oldVault = upgradeableRegistry.userVaults(user1);

        // Test UserVaultUpgraded event - we can't predict the new vault address
        // so we just check that the event is emitted with the correct user and old vault
        address[] memory users = new address[](1);
        users[0] = user1;

        vm.prank(admin);
        vm.expectEmit(true, true, false, false); // Only check first two indexed parameters
        emit InvitationRegistryUpgradeable.UserVaultUpgraded(
            user1,
            oldVault,
            address(0) // We don't check the new vault address
        );
        upgradeableRegistry.batchUpgradeVaults(users);
    }
}
