# 链下查询服务完整指南

## 🎯 概述

本指南介绍如何使用Alchemy API完全替代所有移除的链上查询函数，构建完整的链下数据库，实现零Gas费用的丰富查询功能。

## 📋 功能对照表

### 已移除的链上函数 → 链下替代方案

| 链上函数 | 链下替代方案 | 功能增强 |
|---------|-------------|----------|
| `getInvitationChain()` | `getInvitationChain()` + `getInvitationChainDetailed()` | ✅ 增加详细信息 |
| `getCurrentDailyInvites()` | `getCurrentDailyInvites()` | ✅ 实时计算 |
| `getInvitedUsers()` | `getInvitedUsers()` + `getInvitedUsersDetailed()` | ✅ 增加详细信息 |
| `batchQuery()` | `batchQuery()` | ✅ 更丰富的返回数据 |

### 保留的链上安全验证函数

| 链上函数 | 用途 | 为什么必须保留 |
|---------|------|---------------|
| `isRegistered()` | 检查用户注册状态 | 🔒 防止重复注册攻击 |
| `canInvite()` | 验证邀请权限 | 🔒 防止非法邀请攻击 |
| `computeVaultAddress()` | 预计算Vault地址 | 🔒 防止地址预测攻击 |

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install alchemy-sdk ethers
```

### 2. 基础使用

```typescript
import { createAlchemyQueryService, Network } from './scripts/alchemy-query-service';

// 创建查询服务
const service = await createAlchemyQueryService(
    'your-alchemy-api-key',
    '0x1234...', // Registry合约地址
    '0xabcd...', // UserVault实现地址
    Network.ETH_MAINNET
);

// 基础查询
const userAddress = '0x1111...';
console.log('用户是否已注册:', await service.isUserRegistered(userAddress));
console.log('用户Vault地址:', await service.getUserVault(userAddress));
console.log('用户邀请人:', await service.getInviterOf(userAddress));
console.log('邀请链条:', await service.getInvitationChain(userAddress));
```

### 3. 构建完整数据库

```bash
# 设置环境变量
export ALCHEMY_API_KEY="your-api-key"
export REGISTRY_ADDRESS="0x1234..."
export VAULT_IMPLEMENTATION_ADDRESS="0xabcd..."
export START_BLOCK="0"
export OUTPUT_DIR="./database-reports"

# 构建完整数据库
npx ts-node scripts/build-invitation-database.ts

# 增量更新数据库
npx ts-node scripts/build-invitation-database.ts --update 12345678
```

## 📊 数据库功能

### 1. 完整用户信息

```typescript
interface UserInfo {
    address: string;
    isRegistered: boolean;
    vault?: string;
    inviter?: string;
    invitedUsers?: string[];
    totalInvites: number;
    dailyInvites: number;
    lastInviteTimestamp: number;
    registrationTimestamp?: number;
    invitationChain?: string[];
}

// 获取完整用户信息
const userInfo = await service.getUserInfo(userAddress);
```

### 2. 系统统计分析

```typescript
// 获取系统总体统计
const systemStats = await service.getSystemStats();
console.log('总用户数:', systemStats.totalUsers);
console.log('总邀请数:', systemStats.totalInvitations);
console.log('活跃邀请者:', systemStats.activeInviters);
console.log('平均邀请数:', systemStats.averageInvitesPerUser);
console.log('Top邀请者:', systemStats.topInviters);
```

### 3. 每日统计分析

```typescript
// 获取过去30天的每日统计
const dailyStats = await service.getDailyStats(30);
for (const day of dailyStats) {
    console.log(`${day.date}: ${day.newRegistrations} 新用户`);
}
```

### 4. 批量查询

```typescript
// 批量查询多个用户信息
const addresses = ['0x1111...', '0x2222...', '0x3333...'];
const batchResults = await service.batchQuery(addresses);
```

## 🔧 高级功能

### 1. 邀请链详细分析

```typescript
// 获取邀请链的详细信息
const chainDetails = await service.getInvitationChainDetailed(userAddress);
for (const user of chainDetails) {
    console.log(`Level ${user.level}: ${user.address} (${user.totalInvites} invites)`);
}
```

### 2. 邀请用户详细信息

```typescript
// 获取用户邀请的所有人的详细信息
const invitedDetails = await service.getInvitedUsersDetailed(inviterAddress);
for (const user of invitedDetails) {
    console.log(`${user.address}: 注册于 ${new Date(user.registrationTimestamp * 1000)}`);
}
```

### 3. 数据库管理

```typescript
// 导出数据库
const exportedData = service.exportDatabase();
fs.writeFileSync('database-backup.json', exportedData);

// 导入数据库
const importedData = fs.readFileSync('database-backup.json', 'utf8');
service.importDatabase(importedData);

// 获取数据库信息
const dbInfo = service.getDatabaseInfo();
console.log('数据库状态:', dbInfo);
```

## 📈 生成的报告

运行数据库构建脚本后，会在输出目录生成以下报告：

### 1. `invitation-database.json`
完整的用户数据库，包含所有用户信息

### 2. `system-stats.json`
系统总体统计信息
```json
{
  "systemStats": {
    "totalUsers": 10000,
    "totalInvitations": 25000,
    "activeInviters": 3500,
    "averageInvitesPerUser": 2.5,
    "topInviters": [...]
  }
}
```

### 3. `daily-stats.json`
每日统计信息，包含过去30天的数据

### 4. `top-inviters.json`
Top邀请者详细信息和分析

### 5. `invitation-chains.json`
邀请链分析报告

## 🔄 实时更新

### 1. 增量更新

```typescript
// 从指定区块开始增量更新
await service.updateDatabase(fromBlock);
```

### 2. 定时更新

```typescript
// 设置定时更新（每小时）
setInterval(async () => {
    const latestBlock = await alchemy.core.getBlockNumber();
    await service.updateDatabase(latestBlock - 100); // 保留100个区块的缓冲
}, 3600000); // 1小时
```

## 🚀 性能优化

### 1. 缓存机制

```typescript
// 服务内置了智能缓存
// 清除缓存
service.clearCache();
```

### 2. 快速查询

```typescript
import { QuickAlchemyQuery } from './scripts/alchemy-query-service';

// 用于简单查询，无需初始化完整数据库
const quickQuery = new QuickAlchemyQuery(apiKey, registryAddress);
const isRegistered = await quickQuery.isRegistered(userAddress);
```

## 🔒 安全注意事项

### 1. 验证函数仍需链上调用

```typescript
// ❌ 错误：仅依赖链下验证
const canInvite = await service.canInviteOffchain(inviter, invitee);

// ✅ 正确：链上验证确保安全
const canInvite = await registryContract.canInvite(inviter, invitee);
```

### 2. 地址预计算

```typescript
// ✅ 链上预计算（安全）
const vaultAddress = await registryContract.computeVaultAddress(userAddress);

// ⚠️ 链下预计算（仅供参考）
const vaultAddress = await service.computeVaultAddress(userAddress);
```

## 💡 最佳实践

### 1. 前端集成

```typescript
// 前端查询流程
class InvitationFrontend {
    constructor(private queryService: AlchemyInvitationQueryService) {}
    
    async loadUserDashboard(userAddress: string) {
        // 1. 链下查询用户信息（免费）
        const userInfo = await this.queryService.getUserInfo(userAddress);
        
        // 2. 链下查询邀请详情（免费）
        const invitedUsers = await this.queryService.getInvitedUsersDetailed(userAddress);
        
        // 3. 链下查询邀请链（免费）
        const invitationChain = await this.queryService.getInvitationChainDetailed(userAddress);
        
        return { userInfo, invitedUsers, invitationChain };
    }
    
    async validateInvitation(inviter: string, invitee: string) {
        // 1. 链下预检查（免费，快速反馈）
        const offchainCheck = await this.queryService.canInviteOffchain(inviter, invitee);
        if (!offchainCheck.canInvite) {
            return { canInvite: false, reason: offchainCheck.reason };
        }
        
        // 2. 链上最终验证（消耗Gas，确保安全）
        const onchainCheck = await registryContract.canInvite(inviter, invitee);
        return { canInvite: onchainCheck };
    }
}
```

### 2. 数据分析

```typescript
// 构建空投分析系统
class AirdropAnalyzer {
    constructor(private queryService: AlchemyInvitationQueryService) {}
    
    async generateAirdropList(criteria: {
        minInvites: number;
        registeredBefore: number;
        chainLengthMin: number;
    }) {
        const systemStats = await this.queryService.getSystemStats();
        const eligibleUsers = [];
        
        for (const topInviter of systemStats.topInviters) {
            if (topInviter.totalInvites >= criteria.minInvites) {
                const userInfo = await this.queryService.getUserInfo(topInviter.address);
                if (userInfo && 
                    userInfo.registrationTimestamp && 
                    userInfo.registrationTimestamp < criteria.registeredBefore &&
                    userInfo.invitationChain &&
                    userInfo.invitationChain.length >= criteria.chainLengthMin) {
                    
                    eligibleUsers.push({
                        address: topInviter.address,
                        totalInvites: topInviter.totalInvites,
                        registrationTimestamp: userInfo.registrationTimestamp,
                        chainLength: userInfo.invitationChain.length
                    });
                }
            }
        }
        
        return eligibleUsers;
    }
}
```

## 🎯 总结

通过这套完整的链下查询系统，我们实现了：

✅ **零Gas查询**：所有复杂查询都免费
✅ **功能增强**：比原链上函数提供更丰富的数据
✅ **完整数据库**：从事件重建的完整用户数据库
✅ **实时更新**：支持增量更新和定时同步
✅ **安全保证**：保留必要的链上验证函数
✅ **性能优化**：智能缓存和批量查询
✅ **易于集成**：简单的API接口和丰富的工具函数

这个设计完美实现了**链上安全验证，链下丰富查询**的架构目标！
