# 付费邀请统计功能实现

## 概述

根据用户需求，我们为邀请系统添加了付费邀请统计功能。当用户通过 MatchPurchase 合约完成链上邀请码注册时，系统会在邀请人的 vault 中记录付费邀请的相关数据。

**核心设计原则：**
- ✅ **统一注册系统**: 只有一套注册流程，避免系统混乱
- ✅ **权限控制**: 通过授权机制确保只有合法的付费合约可以调用
- ✅ **正确的调用流程**: MatchPurchase 检查注册状态 → 调用 Registry 的专门付费注册函数

## 功能特性

### 新增数据字段

在 `UserVault` 和 `UserVaultUpgradeable` 合约中添加了以下状态变量：

```solidity
// 付费邀请统计
uint32 public totalPayingInvites;      // 通过付费方式邀请的总人数
uint32 public dailyPayingInvites;      // 当日通过付费方式邀请的人数
uint32 public lastPayingInviteTimestamp; // 最后付费邀请时间戳
```

### 核心功能

1. **付费邀请记录**: 当用户通过 MatchPurchase 注册时，邀请人的 vault 会同时更新：
   - 总邀请数 (`totalInvites`)
   - 付费邀请总数 (`totalPayingInvites`)
   - 当日邀请数 (`dailyInvites`)
   - 当日付费邀请数 (`dailyPayingInvites`)

2. **每日重置**: 付费邀请统计也支持每日重置，与现有的邀请统计逻辑保持一致

3. **防重复邀请**: 继承现有的防重复邀请机制

## 系统架构

### 调用流程

1. **用户调用 MatchPurchase.registerAndPurchase()**
2. **MatchPurchase 检查用户注册状态**: `registry.isRegistered(msg.sender)`
3. **MatchPurchase 调用付费注册**: `registry.registerWithPayment(msg.sender, inviter)`
4. **Registry 验证调用者权限**: `authorizedPaymentContracts[msg.sender]`
5. **Registry 执行注册并记录付费邀请统计**

### 权限控制

- Registry 维护 `authorizedPaymentContracts` 映射
- 只有被授权的合约（如 MatchPurchase）才能调用 `registerWithPayment`
- 部署时自动授权 MatchPurchase 合约

## 实现细节

### 1. UserVault 合约更新

添加了新的函数 `recordPayingInvitation()`:

```solidity
function recordPayingInvitation(address invitee) external onlyRegistry {
    // 防止重复邀请同一用户
    if (hasInvited[invitee]) return;

    hasInvited[invitee] = true;

    // 更新每日邀请统计
    uint32 currentTime = uint32(block.timestamp);
    if (currentTime - lastInviteTimestamp >= 1 days) {
        dailyInvites = 1;
    } else {
        ++dailyInvites;
    }
    lastInviteTimestamp = currentTime;
    ++totalInvites;
    
    // 更新付费邀请统计
    if (currentTime - lastPayingInviteTimestamp >= 1 days) {
        dailyPayingInvites = 1;
    } else {
        ++dailyPayingInvites;
    }
    lastPayingInviteTimestamp = currentTime;
    ++totalPayingInvites;
}
```

### 2. InvitationRegistry 合约更新

添加了权限控制和专门的付费注册函数：

```solidity
// 授权的付费合约地址
mapping(address => bool) public authorizedPaymentContracts;

// 授权付费合约
function setAuthorizedPaymentContract(address contractAddr, bool authorized) external onlyOwner {
    authorizedPaymentContracts[contractAddr] = authorized;
}

// 付费注册函数
function registerWithPayment(address user, address inviter) external whenNotPaused returns (address vault) {
    // 只允许授权的合约调用
    require(authorizedPaymentContracts[msg.sender], "Unauthorized payment contract");

    // 执行注册逻辑...

    // 更新邀请人记录（使用付费邀请记录）
    if (inviter != address(0)) {
        UserVault(userVaults[inviter]).recordPayingInvitation(user);
        // 发出事件...
    }
}
```

### 3. MatchPurchase 合约更新

修改 `registerAndPurchase()` 函数的完整流程：

```solidity
function registerAndPurchase(address inviter, uint256 count) external payable whenNotPaused {
    // 检查用户是否已注册（调用 Registry 的判断函数）
    require(!registry.isRegistered(msg.sender), "User already registered");

    // 计算费用...

    // 先注册用户（调用付费注册流程）
    address vault = registry.registerWithPayment(msg.sender, inviter);

    // 继续购买逻辑...
}
```

### 4. 事件更新

添加了新的事件来跟踪付费邀请：

```solidity
event PayingInvitationConfirmed(
    address indexed inviter,
    address indexed invitee,
    uint256 totalPayingInvites
);
```

## 数据统计示例

假设用户A作为邀请人，有以下邀请记录：

1. 用户B通过付费方式注册 (MatchPurchase)
2. 用户C通过免费方式注册 (直接调用 register)
3. 用户D通过付费方式注册 (MatchPurchase)

最终用户A的统计数据：
- `totalInvites`: 3 (总邀请数)
- `totalPayingInvites`: 2 (付费邀请数)
- `dailyInvites`: 3 (当日邀请数)
- `dailyPayingInvites`: 2 (当日付费邀请数)

## 兼容性

- ✅ 向后兼容：现有的免费注册流程不受影响
- ✅ 升级友好：新字段使用默认值，不影响现有数据
- ✅ Gas 优化：使用 uint32 类型和前缀递增操作
- ✅ 时间一致性：使用与现有系统相同的时间戳计算逻辑

## 测试验证

通过模拟测试验证了以下场景：
- ✅ 付费邀请正确计数
- ✅ 免费邀请不影响付费统计
- ✅ 每日重置功能正常
- ✅ 防重复邀请机制有效

## 部署注意事项

1. 需要同时部署更新的 Registry 和 Vault 合约
2. MatchPurchase 合约需要更新以调用新的注册函数
3. 前端需要更新以显示新的付费邀请统计数据
4. 事件索引服务需要处理新的 PayingInvitationConfirmed 事件

## 总结

这个功能简洁地实现了用户需求，在邀请人的 vault 中增加了付费邀请的统计数据，同时保持了与现有系统的一致性和兼容性。实现遵循了最小化原则，只添加必要的状态变量和函数，确保了 gas 效率和代码可维护性。
