// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "lib/solady/src/auth/Ownable.sol";
import "./InvitationRegistryUpgradeable.sol";

/**
 * @title MatchPurchaseUpgradeable
 * @notice 用户购买match次数的合约，支持MON代币支付 - 可升级版本
 * @dev 集成可升级邀请系统，支持未注册用户一键注册+购买
 */
contract MatchPurchaseUpgradeable is Ownable {
    
    // ========== 状态变量 ==========
    
    /// @notice 邀请注册合约地址
    InvitationRegistryUpgradeable public immutable registry;
    
    /// @notice 基础价格 (0.08 MON)
    uint256 public constant BASE_PRICE = 0.08 ether;
    
    /// @notice 价格递增比例 (108% = 1.08)
    uint256 public constant PRICE_MULTIPLIER = 108;
    uint256 public constant PRICE_DIVISOR = 100;
    
    /// @notice 注册费用 (如果用户未注册需要额外支付)
    uint256 public registrationFee = 0.01 ether;
    
    /// @notice 用户每日购买次数
    mapping(address => uint256) public dailyPurchases;

    /// @notice 用户最后购买时间戳 (user => timestamp)
    mapping(address => uint32) public lastPurchaseTimestamp;
    
    // ========== 事件 ==========
    
    event MatchPurchased(
        address indexed user,
        uint256 indexed day,
        uint256 purchaseCount,
        uint256 totalCost,
        uint256 timestamp
    );
    
    event UserRegisteredAndPurchased(
        address indexed user,
        address indexed inviter,
        address indexed vault,
        uint256 purchaseCount,
        uint256 totalCost,
        uint256 timestamp
    );
    
    event FundsWithdrawn(
        address indexed owner,
        uint256 amount,
        uint256 timestamp
    );
    
    event RegistrationFeeUpdated(
        uint256 oldFee,
        uint256 newFee
    );
    
    // ========== 错误 ==========
    
    error InsufficientPayment();
    error InvalidPurchaseCount();
    error InvalidInviter();
    error WithdrawalFailed();
    error InvalidRegistrationFee();
    
    // ========== 构造函数 ==========
    
    constructor(address _registry) Ownable(msg.sender) {
        require(_registry != address(0), "Invalid registry address");
        registry = InvitationRegistryUpgradeable(_registry);
    }
    
    // ========== 外部函数 ==========
    
    /**
     * @notice 购买match次数（用户已注册）
     * @param count 购买次数
     */
    function purchaseMatches(uint256 count) external payable {
        if (count == 0) revert InvalidPurchaseCount();
        
        // 检查用户是否已注册
        require(registry.isRegistered(msg.sender), "User not registered");
        
        uint256 totalCost = calculateTotalCost(msg.sender, count);
        if (msg.value < totalCost) revert InsufficientPayment();
        
        // 更新购买记录（使用与Vault相同的时间计算逻辑）
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastPurchaseTimestamp[msg.sender] >= 1 days) {
            dailyPurchases[msg.sender] = count;
        } else {
            dailyPurchases[msg.sender] += count;
        }
        lastPurchaseTimestamp[msg.sender] = currentTime;

        uint256 currentDay = getCurrentDay();
        emit MatchPurchased(msg.sender, currentDay, count, totalCost, block.timestamp);
        
        // 退还多余的MON
        if (msg.value > totalCost) {
            payable(msg.sender).transfer(msg.value - totalCost);
        }
    }
    
    /**
     * @notice 注册并购买match次数（用户未注册）
     * @param inviter 邀请人地址
     * @param count 购买次数
     */
    function registerAndPurchase(address inviter, uint256 count) external payable {
        if (count == 0) revert InvalidPurchaseCount();
        if (inviter == msg.sender) revert InvalidInviter();
        
        // 检查用户是否已注册
        require(!registry.isRegistered(msg.sender), "User already registered");
        
        // 计算总费用（购买费用 + 注册费用）
        uint256 purchaseCost = calculateTotalCost(msg.sender, count);
        uint256 totalCost = purchaseCost + registrationFee;
        
        if (msg.value < totalCost) revert InsufficientPayment();
        
        // 先注册用户（使用付费注册方式）
        address vault = registry.registerWithPayment(msg.sender, inviter);
        
        // 更新购买记录（使用与Vault相同的时间计算逻辑）
        uint32 currentTime = uint32(block.timestamp);
        dailyPurchases[msg.sender] = count;
        lastPurchaseTimestamp[msg.sender] = currentTime;
        
        emit UserRegisteredAndPurchased(
            msg.sender, 
            inviter, 
            vault, 
            count, 
            totalCost, 
            block.timestamp
        );
        
        // 退还多余的MON
        if (msg.value > totalCost) {
            payable(msg.sender).transfer(msg.value - totalCost);
        }
    }
    
    // ========== 查询函数 ==========
    
    /**
     * @notice 计算购买指定次数的总费用
     * @param user 用户地址
     * @param count 购买次数
     * @return totalCost 总费用
     */
    function calculateTotalCost(address user, uint256 count) public view returns (uint256 totalCost) {
        // 获取当前用户的每日购买次数（使用与Vault相同的时间计算逻辑）
        uint256 currentDailyPurchases = getCurrentDailyPurchases(user);

        for (uint256 i = 0; i < count; i++) {
            uint256 currentPrice = BASE_PRICE;

            // 计算当前次数的价格（基于当日已购买次数）
            for (uint256 j = 0; j < currentDailyPurchases + i; j++) {
                currentPrice = (currentPrice * PRICE_MULTIPLIER) / PRICE_DIVISOR;
            }

            totalCost += currentPrice;
        }
    }

    /**
     * @notice 获取当前天数（基于区块时间戳）
     * @return 当前天数
     */
    function getCurrentDay() public view returns (uint256) {
        return block.timestamp / 1 days;
    }

    /**
     * @notice 获取用户当日购买次数（使用与Vault相同的时间计算逻辑）
     * @param user 用户地址
     * @return 当日购买次数
     */
    function getCurrentDailyPurchases(address user) public view returns (uint256) {
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastPurchaseTimestamp[user] >= 1 days) {
            return 0; // 新的一天，重置为0
        } else {
            return dailyPurchases[user]; // 同一天内的购买次数
        }
    }

    /**
     * @notice 获取用户当日购买次数（外部接口）
     * @param user 用户地址
     * @return 当日购买次数
     */
    function getUserDailyPurchases(address user) external view returns (uint256) {
        return getCurrentDailyPurchases(user);
    }

    /**
     * @notice 获取下一次购买的价格
     * @param user 用户地址
     * @return 下一次购买价格
     */
    function getNextPurchasePrice(address user) external view returns (uint256) {
        uint256 currentDailyPurchases = getCurrentDailyPurchases(user);

        uint256 price = BASE_PRICE;
        for (uint256 i = 0; i < currentDailyPurchases; i++) {
            price = (price * PRICE_MULTIPLIER) / PRICE_DIVISOR;
        }

        return price;
    }
    
    // ========== 管理员函数 ==========
    
    /**
     * @notice 提取合约中的MON（仅owner）
     */
    function withdrawFunds() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        if (!success) revert WithdrawalFailed();
        
        emit FundsWithdrawn(owner(), balance, block.timestamp);
    }
    
    /**
     * @notice 更新注册费用（仅owner）
     * @param newFee 新的注册费用
     */
    function updateRegistrationFee(uint256 newFee) external onlyOwner {
        if (newFee > 1 ether) revert InvalidRegistrationFee(); // 防止设置过高费用
        
        uint256 oldFee = registrationFee;
        registrationFee = newFee;
        
        emit RegistrationFeeUpdated(oldFee, newFee);
    }
    
    /**
     * @notice 获取合约余额
     * @return 合约MON余额
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    // ========== 接收MON ==========
    
    receive() external payable {}
    fallback() external payable {}
}
