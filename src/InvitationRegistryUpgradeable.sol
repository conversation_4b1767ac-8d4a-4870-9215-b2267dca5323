// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "lib/solady/src/utils/Clone.sol";
import "lib/solady/src/auth/Ownable.sol";
import "./UserVaultUpgradeable.sol";

/**
 * @title InvitationRegistryUpgradeable
 * <AUTHOR> Project
 * @notice 去中心化邀请系统的核心注册合约 - 可升级版本
 * @dev 使用最小代理模式部署用户Vault，支持Vault实现升级
 */
contract InvitationRegistryUpgradeable is Ownable {
    using Clone for address;
    
    // ========== 状态变量 ==========
    
    /// @notice 用户地址到Vault地址的映射（核心存储）
    mapping(address => address) public userVaults;

    /// @notice 用户地址到邀请人地址的映射（验证必需，不能移除）
    mapping(address => address) public inviterOf;
    
    /// @notice 系统总用户数（uint32足够）
    uint32 public totalUsers;
    
    /// @notice 根用户地址（第一个用户）
    address public immutable rootUser;
    
    /// @notice 当前Vault实现合约地址（可升级版本）
    address public vaultImplementation;

    /// @notice 授权的付费合约地址（如MatchPurchase）
    mapping(address => bool) public authorizedPaymentContracts;

    // ========== 事件 ==========
    
    event UserRegistered(
        address indexed user,
        address indexed inviter,
        address indexed vault,
        uint256 timestamp
    );
    
    event InvitationConfirmed(
        address indexed inviter,
        address indexed invitee,
        uint256 inviterTotalInvites
    );

    event PayingInvitationConfirmed(
        address indexed inviter,
        address indexed invitee,
        uint256 totalPayingInvites
    );

    event VaultImplementationUpgraded(
        address indexed oldImplementation,
        address indexed newImplementation
    );

    event UserVaultUpgraded(
        address indexed user,
        address indexed oldVault,
        address indexed newVault
    );
    
    // ========== 错误 ==========
    
    error AlreadyRegistered();
    error InvalidInviter();
    error CannotSelfInvite();
    error Unauthorized();
    error InvalidImplementation();
    
    // ========== 构造函数 ==========
    
    constructor(address _rootUser) Ownable(msg.sender) {
        rootUser = _rootUser;
        vaultImplementation = address(new UserVaultUpgradeable());

        // 为根用户创建Vault
        _createVaultForUser(rootUser, address(0));
    }
    
    // ========== 外部函数 ==========
    
    /**
     * @notice 通过邀请人注册
     * @param inviter 邀请人地址
     * @return vault 新创建的用户Vault地址
     */
    function register(address inviter) external whenNotPaused returns (address vault) {
        if (userVaults[msg.sender] != address(0)) revert AlreadyRegistered();
        if (inviter == msg.sender) revert CannotSelfInvite();

        // 验证邀请人（简化验证，复杂检查在链下完成）
        if (userVaults[inviter] == address(0) && inviter != rootUser) {
            revert InvalidInviter();
        }

        // 检查是否重复邀请
        if (inviter != address(0) && userVaults[inviter] != address(0)) {
            UserVaultUpgradeable inviterVault = UserVaultUpgradeable(userVaults[inviter]);
            require(!inviterVault.hasInvited(msg.sender), "Already invited");
        }

        // 创建用户Vault
        vault = _createVaultForUser(msg.sender, inviter);

        // 更新邀请人记录
        if (inviter != address(0)) {
            UserVaultUpgradeable(userVaults[inviter]).recordInvitation(msg.sender);
            uint32 totalInvites = UserVaultUpgradeable(userVaults[inviter]).totalInvites();
            emit InvitationConfirmed(inviter, msg.sender, totalInvites);
        }

        emit UserRegistered(msg.sender, inviter, vault, block.timestamp);
    }

    /**
     * @notice 用户通过付费方式注册（由授权合约调用，如MatchPurchase）
     * @param user 用户地址
     * @param inviter 邀请人地址
     * @return vault 创建的用户Vault地址
     * @dev 这个函数专门用于付费注册，会记录付费邀请统计
     */
    function registerWithPayment(address user, address inviter) external whenNotPaused returns (address vault) {
        // 只允许授权的合约调用
        require(authorizedPaymentContracts[msg.sender], "Unauthorized payment contract");

        // 防止重复注册
        if (userVaults[user] != address(0)) revert AlreadyRegistered();
        if (inviter == user) revert CannotSelfInvite();

        // 验证邀请人（简化验证，复杂检查在链下完成）
        if (userVaults[inviter] == address(0) && inviter != rootUser) {
            revert InvalidInviter();
        }

        // 检查是否重复邀请
        if (inviter != address(0) && userVaults[inviter] != address(0)) {
            UserVaultUpgradeable inviterVault = UserVaultUpgradeable(userVaults[inviter]);
            require(!inviterVault.hasInvited(user), "Already invited");
        }

        // 创建用户Vault
        vault = _createVaultForUser(user, inviter);

        // 更新邀请人记录（使用付费邀请记录）
        if (inviter != address(0)) {
            UserVaultUpgradeable(userVaults[inviter]).recordPayingInvitation(user);
            uint32 totalInvites = UserVaultUpgradeable(userVaults[inviter]).totalInvites();
            uint32 totalPayingInvites = UserVaultUpgradeable(userVaults[inviter]).totalPayingInvites();
            emit InvitationConfirmed(inviter, user, totalInvites);
            emit PayingInvitationConfirmed(inviter, user, totalPayingInvites);
        }

        emit UserRegistered(user, inviter, vault, block.timestamp);
    }

    /**
     * @notice 授权付费合约（只有owner可以调用）
     * @param contractAddr 合约地址
     * @param authorized 是否授权
     */
    function setAuthorizedPaymentContract(address contractAddr, bool authorized) external onlyOwner {
        authorizedPaymentContracts[contractAddr] = authorized;
    }

    // ========== 安全验证函数（必须保留） ==========

    /**
     * @notice 检查用户是否已注册（安全验证必需）
     * @param user 用户地址
     * @return 是否已注册
     */
    function isRegistered(address user) external view returns (bool) {
        return userVaults[user] != address(0);
    }

    /**
     * @notice 检查是否可以邀请某用户（安全验证必需）
     * @param inviter 邀请人地址
     * @param invitee 被邀请人地址
     * @return 是否可以邀请
     */
    function canInvite(address inviter, address invitee) external view returns (bool) {
        // 基本安全检查
        if (inviter == invitee) return false;
        if (userVaults[inviter] == address(0) && inviter != rootUser) return false;
        if (userVaults[invitee] != address(0)) return false;

        // 检查是否已经邀请过
        if (userVaults[inviter] != address(0)) {
            UserVaultUpgradeable inviterVault = UserVaultUpgradeable(userVaults[inviter]);
            return !inviterVault.hasInvited(invitee);
        }

        return true;
    }

    /**
     * @notice 预计算用户的Vault地址（安全验证必需）
     * @param user 用户地址
     * @return 预计算的Vault地址
     * ⚠️ 不能移除：防止前端地址预测攻击，确保工作流程完整性
     */
    function computeVaultAddress(address user) external view returns (address) {
        bytes32 salt = keccak256(abi.encodePacked(user));
        return vaultImplementation.predictDeterministicAddress(salt);
    }

    // ========== 纯查询函数已移除（用链下API替代） ==========
    // getInvitationChain, getCurrentDailyInvites 等纯查询函数已移除

    // ========== 升级管理函数 ==========

    /**
     * @notice 升级Vault实现合约（仅owner）
     * @param newImplementation 新的Vault实现地址
     */
    function upgradeVaultImplementation(address newImplementation) external onlyOwner {
        if (newImplementation == address(0)) revert InvalidImplementation();

        address oldImplementation = vaultImplementation;
        vaultImplementation = newImplementation;

        emit VaultImplementationUpgraded(oldImplementation, newImplementation);
    }

    /**
     * @notice 批量升级用户Vault（仅owner）
     * @param users 需要升级的用户地址数组
     */
    function batchUpgradeVaults(address[] calldata users) external onlyOwner {
        for (uint256 i = 0; i < users.length; ++i) {
            address user = users[i];
            address oldVault = userVaults[user];

            if (oldVault != address(0)) {
                address newVault = _createUpgradedVault(user, oldVault);
                userVaults[user] = newVault;

                emit UserVaultUpgraded(user, oldVault, newVault);
            }
        }
    }

    // ========== 管理员函数 ==========

    /**
     * @notice 紧急暂停注册（仅owner）
     */
    bool public registrationPaused = false;

    function pauseRegistration() external onlyOwner {
        registrationPaused = true;
    }

    function unpauseRegistration() external onlyOwner {
        registrationPaused = false;
    }

    modifier whenNotPaused() {
        require(!registrationPaused, "Registration paused");
        _;
    }
    
    // ========== 内部函数 ==========

    function _createVaultForUser(address user, address inviter) internal returns (address vault) {
        bytes32 salt = keccak256(abi.encodePacked(user));
        vault = vaultImplementation.cloneDeterministic(salt);

        UserVaultUpgradeable(vault).initialize(user, inviter);

        userVaults[user] = vault;
        inviterOf[user] = inviter;

        ++totalUsers;
    }

    function _createUpgradedVault(address user, address oldVault) internal returns (address newVault) {
        // 从旧Vault读取数据
        UserVaultUpgradeable oldVaultContract = UserVaultUpgradeable(oldVault);
        address inviter = oldVaultContract.inviter();
        uint32 totalInvites = oldVaultContract.totalInvites();
        uint32 dailyInvites = oldVaultContract.dailyInvites();
        uint32 lastInviteTimestamp = oldVaultContract.lastInviteTimestamp();

        // 创建新Vault
        bytes32 salt = keccak256(abi.encodePacked(user, block.timestamp));
        newVault = Clone.cloneDeterministic(vaultImplementation, salt);

        // 初始化新Vault并迁移数据
        UserVaultUpgradeable(newVault).initializeWithMigration(
            user,
            inviter,
            totalInvites,
            dailyInvites,
            lastInviteTimestamp
        );
    }
}


