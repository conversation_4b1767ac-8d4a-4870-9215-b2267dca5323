// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

/**
 * @title UserVault
 * @notice 用户的个人数据存储合约 - 原始版本（不可升级）
 * @dev 通过最小代理模式部署，每个用户拥有独立实例
 */
contract UserVault {
    // ========== 状态变量 ==========

    address public owner;
    address public inviter;
    address public immutable registry;  // Registry地址在构造时确定，不可更改
    bool public initialized;

    // 核心邀请统计（最小化设计）
    uint32 public totalInvites;            // 总邀请数（uint32足够，节省gas）
    uint32 public dailyInvites;            // 当日邀请数
    uint32 public lastInviteTimestamp;     // 最后邀请时间戳（uint32足够到2106年）

    // 付费邀请统计
    uint32 public totalPayingInvites;      // 通过付费方式邀请的总人数
    uint32 public dailyPayingInvites;      // 当日通过付费方式邀请的人数
    uint32 public lastPayingInviteTimestamp; // 最后付费邀请时间戳

    // 防重复邀请检查
    mapping(address => bool) public hasInvited;  // 是否已邀请过此用户
    
    // ========== 错误 ==========

    error AlreadyInitialized();
    error Unauthorized();

    // ========== 修饰器 ==========

    modifier onlyRegistry() {
        if (msg.sender != registry) revert Unauthorized();
        _;
    }

    // ========== 构造函数 ==========

    /**
     * @notice 构造函数 - 设置Registry地址
     * @param _registry InvitationRegistry合约地址
     * @dev Registry地址在部署时确定，不可更改，确保安全性
     */
    constructor(address _registry) {
        require(_registry != address(0), "Invalid registry address");
        registry = _registry;
    }

    // ========== 函数 ==========

    /**
     * @notice 初始化合约（新用户注册时调用）
     * @dev 只能由预设的Registry合约调用
     */
    function initialize(address _owner, address _inviter) external onlyRegistry {
        if (initialized) revert AlreadyInitialized();

        owner = _owner;
        inviter = _inviter;
        initialized = true;
    }




    
    /**
     * @notice 记录新的邀请（只能由Registry调用）
     * @param invitee 被邀请人地址
     */
    function recordInvitation(address invitee) external onlyRegistry {
        // 防止重复邀请同一用户
        if (hasInvited[invitee]) return;

        hasInvited[invitee] = true;

        // 更新每日邀请统计（基于时间戳，优化gas）
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastInviteTimestamp >= 1 days) {
            dailyInvites = 1;
        } else {
            ++dailyInvites;  // 前缀递增更省gas
        }
        lastInviteTimestamp = currentTime;
        ++totalInvites;  // 前缀递增更省gas
    }

    /**
     * @notice 记录通过付费方式的邀请（只能由Registry或授权合约调用）
     * @param invitee 被邀请人地址
     * @dev 这个函数专门用于记录通过MatchPurchase等付费合约完成的邀请
     */
    function recordPayingInvitation(address invitee) external onlyRegistry {
        // 防止重复邀请同一用户
        if (hasInvited[invitee]) return;

        hasInvited[invitee] = true;

        // 更新每日邀请统计（基于时间戳，优化gas）
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastInviteTimestamp >= 1 days) {
            dailyInvites = 1;
        } else {
            ++dailyInvites;  // 前缀递增更省gas
        }
        lastInviteTimestamp = currentTime;
        ++totalInvites;  // 前缀递增更省gas

        // 更新付费邀请统计
        if (currentTime - lastPayingInviteTimestamp >= 1 days) {
            dailyPayingInvites = 1;
        } else {
            ++dailyPayingInvites;  // 前缀递增更省gas
        }
        lastPayingInviteTimestamp = currentTime;
        ++totalPayingInvites;  // 前缀递增更省gas
    }

    // ========== 链下查询函数已移除 ==========
    // 所有查询通过 Alchemy API + 事件索引在链下完成
    // 只保留核心的 public 状态变量自动生成的 getter
    

}
