// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

/**
 * @title UserVaultUpgradeable
 * @notice 用户的个人数据存储合约 - 可升级版本
 * @dev 支持数据迁移升级机制
 */
contract UserVaultUpgradeable {
    // ========== 状态变量 ==========

    address public owner;
    address public inviter;
    address public registry;  // 存储Registry地址，初始化后不可更改
    bool public initialized;

    // 核心邀请统计（最小化设计）
    uint32 public totalInvites;            // 总邀请数（uint32足够，节省gas）
    uint32 public dailyInvites;            // 当日邀请数
    uint32 public lastInviteTimestamp;     // 最后邀请时间戳（uint32足够到2106年）

    // 付费邀请统计
    uint32 public totalPayingInvites;      // 通过付费方式邀请的总人数
    uint32 public dailyPayingInvites;      // 当日通过付费方式邀请的人数
    uint32 public lastPayingInviteTimestamp; // 最后付费邀请时间戳

    // 防重复邀请检查
    mapping(address => bool) public hasInvited;  // 是否已邀请过此用户
    
    // ========== 错误 ==========

    error AlreadyInitialized();
    error Unauthorized();

    // ========== 修饰器 ==========

    modifier onlyRegistry() {
        if (msg.sender != registry) revert Unauthorized();
        _;
    }

    // ========== 函数 ==========

    /**
     * @notice 初始化合约（新用户注册时调用）
     * @dev registry在第一次初始化时设置，之后不可更改
     */
    function initialize(address _owner, address _inviter) external {
        if (initialized) revert AlreadyInitialized();

        // 只有在registry未设置时才允许设置（防止重复初始化攻击）
        if (registry == address(0)) {
            registry = msg.sender;
        } else {
            if (msg.sender != registry) revert Unauthorized();
        }

        owner = _owner;
        inviter = _inviter;
        initialized = true;
    }

    /**
     * @notice 带数据迁移的初始化（升级时调用）
     * @param _owner 用户地址
     * @param _inviter 邀请人地址
     * @param _totalInvites 总邀请数
     * @param _dailyInvites 当日邀请数
     * @param _lastInviteTimestamp 最后邀请时间戳
     */
    function initializeWithMigration(
        address _owner,
        address _inviter,
        uint32 _totalInvites,
        uint32 _dailyInvites,
        uint32 _lastInviteTimestamp
    ) external {
        if (initialized) revert AlreadyInitialized();

        // 只允许已设置的registry调用迁移初始化
        if (registry == address(0) || msg.sender != registry) revert Unauthorized();

        owner = _owner;
        inviter = _inviter;
        totalInvites = _totalInvites;
        dailyInvites = _dailyInvites;
        lastInviteTimestamp = _lastInviteTimestamp;
        // 付费邀请统计初始化为0（新功能）
        totalPayingInvites = 0;
        dailyPayingInvites = 0;
        lastPayingInviteTimestamp = 0;
        initialized = true;
    }

    /**
     * @notice 带完整数据迁移的初始化（包含付费邀请数据）
     * @param _owner 用户地址
     * @param _inviter 邀请人地址
     * @param _totalInvites 总邀请数
     * @param _dailyInvites 当日邀请数
     * @param _lastInviteTimestamp 最后邀请时间戳
     * @param _totalPayingInvites 总付费邀请数
     * @param _dailyPayingInvites 当日付费邀请数
     * @param _lastPayingInviteTimestamp 最后付费邀请时间戳
     */
    function initializeWithFullMigration(
        address _owner,
        address _inviter,
        uint32 _totalInvites,
        uint32 _dailyInvites,
        uint32 _lastInviteTimestamp,
        uint32 _totalPayingInvites,
        uint32 _dailyPayingInvites,
        uint32 _lastPayingInviteTimestamp
    ) external {
        if (initialized) revert AlreadyInitialized();

        // 只允许已设置的registry调用迁移初始化
        if (registry == address(0) || msg.sender != registry) revert Unauthorized();

        owner = _owner;
        inviter = _inviter;
        totalInvites = _totalInvites;
        dailyInvites = _dailyInvites;
        lastInviteTimestamp = _lastInviteTimestamp;
        totalPayingInvites = _totalPayingInvites;
        dailyPayingInvites = _dailyPayingInvites;
        lastPayingInviteTimestamp = _lastPayingInviteTimestamp;
        initialized = true;
    }
    
    /**
     * @notice 记录新的邀请（只能由Registry调用）
     * @param invitee 被邀请人地址
     */
    function recordInvitation(address invitee) external onlyRegistry {
        // 防止重复邀请同一用户
        if (hasInvited[invitee]) return;

        hasInvited[invitee] = true;

        // 更新每日邀请统计（基于时间戳，优化gas）
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastInviteTimestamp >= 1 days) {
            dailyInvites = 1;
        } else {
            ++dailyInvites;  // 前缀递增更省gas
        }
        lastInviteTimestamp = currentTime;
        ++totalInvites;  // 前缀递增更省gas
    }

    /**
     * @notice 记录通过付费方式的邀请（只能由Registry或授权合约调用）
     * @param invitee 被邀请人地址
     * @dev 这个函数专门用于记录通过MatchPurchase等付费合约完成的邀请
     */
    function recordPayingInvitation(address invitee) external onlyRegistry {
        // 防止重复邀请同一用户
        if (hasInvited[invitee]) return;

        hasInvited[invitee] = true;

        // 更新每日邀请统计（基于时间戳，优化gas）
        uint32 currentTime = uint32(block.timestamp);
        if (currentTime - lastInviteTimestamp >= 1 days) {
            dailyInvites = 1;
        } else {
            ++dailyInvites;  // 前缀递增更省gas
        }
        lastInviteTimestamp = currentTime;
        ++totalInvites;  // 前缀递增更省gas

        // 更新付费邀请统计
        if (currentTime - lastPayingInviteTimestamp >= 1 days) {
            dailyPayingInvites = 1;
        } else {
            ++dailyPayingInvites;  // 前缀递增更省gas
        }
        lastPayingInviteTimestamp = currentTime;
        ++totalPayingInvites;  // 前缀递增更省gas
    }

    // ========== 链下查询函数已移除 ==========
    // 所有查询通过 Alchemy API + 事件索引在链下完成
    // 只保留核心的 public 状态变量自动生成的 getter
    

}
