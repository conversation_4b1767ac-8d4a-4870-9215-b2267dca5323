import { ethers } from 'ethers';

// 事件签名
const MATCH_PURCHASED_TOPIC = ethers.utils.id("MatchPurchased(address,uint256,uint256,uint256,uint256)");
const USER_REGISTERED_AND_PURCHASED_TOPIC = ethers.utils.id("UserRegisteredAndPurchased(address,address,address,uint256,uint256,uint256)");

// 接口定义
interface MatchPurchasedEvent {
  user: string;
  day: number;
  purchaseCount: number;
  totalCost: string;
  timestamp: number;
  txHash: string;
  blockNumber: number;
}

interface UserRegisteredAndPurchasedEvent {
  user: string;
  inviter: string;
  vault: string;
  purchaseCount: number;
  totalCost: string;
  timestamp: number;
  txHash: string;
  blockNumber: number;
}

interface UserPurchaseStats {
  totalPurchases: number;
  totalSpent: string;
  dailyPurchases: { [date: string]: number };
  lastPurchaseDate: string;
  averagePurchaseSize: number;
}

export class MatchPurchaseQueryService {
  private provider: ethers.providers.JsonRpcProvider;
  private matchPurchaseAddress: string;

  constructor(rpcUrl: string, matchPurchaseAddress: string) {
    this.provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    this.matchPurchaseAddress = matchPurchaseAddress;
  }

  /**
   * 查询用户的所有购买记录
   */
  async getUserPurchaseHistory(
    userAddress: string,
    fromBlock: number = 0,
    toBlock: number | string = 'latest'
  ): Promise<MatchPurchasedEvent[]> {
    const filter = {
      address: this.matchPurchaseAddress,
      topics: [
        MATCH_PURCHASED_TOPIC,
        ethers.utils.hexZeroPad(userAddress.toLowerCase(), 32)
      ],
      fromBlock,
      toBlock
    };

    const logs = await this.provider.getLogs(filter);
    
    return logs.map(log => {
      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['uint256', 'uint256', 'uint256', 'uint256'],
        log.data
      );

      return {
        user: userAddress,
        day: decoded[0].toNumber(),
        purchaseCount: decoded[1].toNumber(),
        totalCost: ethers.utils.formatEther(decoded[2]),
        timestamp: decoded[3].toNumber(),
        txHash: log.transactionHash,
        blockNumber: log.blockNumber
      };
    });
  }

  /**
   * 查询用户注册并购买的记录
   */
  async getUserRegistrationPurchases(
    userAddress: string,
    fromBlock: number = 0,
    toBlock: number | string = 'latest'
  ): Promise<UserRegisteredAndPurchasedEvent[]> {
    const filter = {
      address: this.matchPurchaseAddress,
      topics: [
        USER_REGISTERED_AND_PURCHASED_TOPIC,
        ethers.utils.hexZeroPad(userAddress.toLowerCase(), 32)
      ],
      fromBlock,
      toBlock
    };

    const logs = await this.provider.getLogs(filter);
    
    return logs.map(log => {
      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['address', 'address', 'uint256', 'uint256', 'uint256'],
        log.data
      );

      return {
        user: userAddress,
        inviter: decoded[0],
        vault: decoded[1],
        purchaseCount: decoded[2].toNumber(),
        totalCost: ethers.utils.formatEther(decoded[3]),
        timestamp: decoded[4].toNumber(),
        txHash: log.transactionHash,
        blockNumber: log.blockNumber
      };
    });
  }

  /**
   * 获取用户购买统计信息
   */
  async getUserPurchaseStats(userAddress: string): Promise<UserPurchaseStats> {
    const purchases = await this.getUserPurchaseHistory(userAddress);
    const registrationPurchases = await this.getUserRegistrationPurchases(userAddress);
    
    // 合并所有购买记录
    const allPurchases = [
      ...purchases.map(p => ({
        count: p.purchaseCount,
        cost: p.totalCost,
        timestamp: p.timestamp
      })),
      ...registrationPurchases.map(p => ({
        count: p.purchaseCount,
        cost: p.totalCost,
        timestamp: p.timestamp
      }))
    ];

    if (allPurchases.length === 0) {
      return {
        totalPurchases: 0,
        totalSpent: '0',
        dailyPurchases: {},
        lastPurchaseDate: '',
        averagePurchaseSize: 0
      };
    }

    // 计算统计信息
    const totalPurchases = allPurchases.reduce((sum, p) => sum + p.count, 0);
    const totalSpent = allPurchases.reduce((sum, p) => sum + parseFloat(p.cost), 0);
    
    // 按日期分组
    const dailyPurchases: { [date: string]: number } = {};
    allPurchases.forEach(p => {
      const date = new Date(p.timestamp * 1000).toISOString().split('T')[0];
      dailyPurchases[date] = (dailyPurchases[date] || 0) + p.count;
    });

    // 最后购买日期
    const lastPurchaseTimestamp = Math.max(...allPurchases.map(p => p.timestamp));
    const lastPurchaseDate = new Date(lastPurchaseTimestamp * 1000).toISOString().split('T')[0];

    return {
      totalPurchases,
      totalSpent: totalSpent.toFixed(6),
      dailyPurchases,
      lastPurchaseDate,
      averagePurchaseSize: totalPurchases / allPurchases.length
    };
  }

  /**
   * 获取所有用户的购买记录（用于分析）
   */
  async getAllPurchases(
    fromBlock: number = 0,
    toBlock: number | string = 'latest'
  ): Promise<MatchPurchasedEvent[]> {
    const filter = {
      address: this.matchPurchaseAddress,
      topics: [MATCH_PURCHASED_TOPIC],
      fromBlock,
      toBlock
    };

    const logs = await this.provider.getLogs(filter);
    
    return logs.map(log => {
      const userAddress = ethers.utils.getAddress('0x' + log.topics[1].slice(26));
      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['uint256', 'uint256', 'uint256', 'uint256'],
        log.data
      );

      return {
        user: userAddress,
        day: decoded[0].toNumber(),
        purchaseCount: decoded[1].toNumber(),
        totalCost: ethers.utils.formatEther(decoded[2]),
        timestamp: decoded[3].toNumber(),
        txHash: log.transactionHash,
        blockNumber: log.blockNumber
      };
    });
  }

  /**
   * 获取系统统计信息
   */
  async getSystemStats(): Promise<{
    totalUsers: number;
    totalPurchases: number;
    totalRevenue: string;
    dailyStats: { [date: string]: { users: number; purchases: number; revenue: string } };
  }> {
    const allPurchases = await this.getAllPurchases();
    const allRegistrations = await this.getAllRegistrationPurchases();

    const uniqueUsers = new Set([
      ...allPurchases.map(p => p.user),
      ...allRegistrations.map(p => p.user)
    ]);

    const totalPurchases = allPurchases.reduce((sum, p) => sum + p.purchaseCount, 0) +
                          allRegistrations.reduce((sum, p) => sum + p.purchaseCount, 0);

    const totalRevenue = allPurchases.reduce((sum, p) => sum + parseFloat(p.totalCost), 0) +
                        allRegistrations.reduce((sum, p) => sum + parseFloat(p.totalCost), 0);

    // 按日期统计
    const dailyStats: { [date: string]: { users: Set<string>; purchases: number; revenue: number } } = {};

    [...allPurchases, ...allRegistrations].forEach(p => {
      const date = new Date(p.timestamp * 1000).toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { users: new Set(), purchases: 0, revenue: 0 };
      }
      dailyStats[date].users.add(p.user);
      dailyStats[date].purchases += p.purchaseCount;
      dailyStats[date].revenue += parseFloat(p.totalCost);
    });

    // 转换为最终格式
    const finalDailyStats: { [date: string]: { users: number; purchases: number; revenue: string } } = {};
    Object.keys(dailyStats).forEach(date => {
      finalDailyStats[date] = {
        users: dailyStats[date].users.size,
        purchases: dailyStats[date].purchases,
        revenue: dailyStats[date].revenue.toFixed(6)
      };
    });

    return {
      totalUsers: uniqueUsers.size,
      totalPurchases,
      totalRevenue: totalRevenue.toFixed(6),
      dailyStats: finalDailyStats
    };
  }

  /**
   * 获取所有注册并购买的记录
   */
  private async getAllRegistrationPurchases(
    fromBlock: number = 0,
    toBlock: number | string = 'latest'
  ): Promise<UserRegisteredAndPurchasedEvent[]> {
    const filter = {
      address: this.matchPurchaseAddress,
      topics: [USER_REGISTERED_AND_PURCHASED_TOPIC],
      fromBlock,
      toBlock
    };

    const logs = await this.provider.getLogs(filter);
    
    return logs.map(log => {
      const userAddress = ethers.utils.getAddress('0x' + log.topics[1].slice(26));
      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['address', 'address', 'uint256', 'uint256', 'uint256'],
        log.data
      );

      return {
        user: userAddress,
        inviter: decoded[0],
        vault: decoded[1],
        purchaseCount: decoded[2].toNumber(),
        totalCost: ethers.utils.formatEther(decoded[3]),
        timestamp: decoded[4].toNumber(),
        txHash: log.transactionHash,
        blockNumber: log.blockNumber
      };
    });
  }
}

// 使用示例
export async function example() {
  const queryService = new MatchPurchaseQueryService(
    'https://testnet-rpc.monad.xyz',
    '0x...' // MatchPurchase合约地址
  );

  // 查询特定用户的购买历史
  const userAddress = '0x...';
  const history = await queryService.getUserPurchaseHistory(userAddress);
  console.log('用户购买历史:', history);

  // 获取用户统计信息
  const stats = await queryService.getUserPurchaseStats(userAddress);
  console.log('用户统计:', stats);

  // 获取系统统计信息
  const systemStats = await queryService.getSystemStats();
  console.log('系统统计:', systemStats);
}
