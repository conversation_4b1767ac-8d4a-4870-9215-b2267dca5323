import { Alchemy, Network } from 'alchemy-sdk';
import { ethers } from 'ethers';

/**
 * 基于Alchemy API的完整链下查询服务
 * 替代所有移除的链上查询函数，构建完整的链下数据库
 * 保证功能最小化，复用原有代码结构
 */

// 数据库接口定义
export interface UserInfo {
    address: string;
    isRegistered: boolean;
    vault?: string;
    inviter?: string;
    invitedUsers?: string[];
    totalInvites: number;
    dailyInvites: number;
    lastInviteTimestamp: number;
    registrationTimestamp?: number;
    invitationChain?: string[];
}

export interface InvitationStats {
    totalUsers: number;
    totalInvitations: number;
    activeInviters: number;
    averageInvitesPerUser: number;
    topInviters: Array<{
        address: string;
        totalInvites: number;
    }>;
}

export interface DailyStats {
    date: string;
    newRegistrations: number;
    totalInvitations: number;
    activeUsers: number;
}
export class AlchemyInvitationQueryService {
    private alchemy: Alchemy;
    private registryAddress: string;
    private vaultImplementationAddress: string;
    private cache: Map<string, any> = new Map();
    private database: Map<string, UserInfo> = new Map(); // 内存数据库
    private isInitialized: boolean = false;

    constructor(
        apiKey: string,
        registryAddress: string,
        vaultImplementationAddress: string,
        network: Network = Network.ETH_MAINNET
    ) {
        this.alchemy = new Alchemy({
            apiKey,
            network
        });
        this.registryAddress = registryAddress;
        this.vaultImplementationAddress = vaultImplementationAddress;
    }

    /**
     * 初始化数据库 - 从区块链事件重建完整数据库
     */
    async initializeDatabase(fromBlock: number = 0): Promise<void> {
        console.log('正在初始化链下数据库...');

        // 获取所有 UserRegistered 事件
        const userRegisteredEvents = await this.getAllUserRegisteredEvents(fromBlock);
        console.log(`找到 ${userRegisteredEvents.length} 个用户注册事件`);

        // 获取所有 InvitationConfirmed 事件
        const invitationEvents = await this.getAllInvitationConfirmedEvents(fromBlock);
        console.log(`找到 ${invitationEvents.length} 个邀请确认事件`);

        // 构建用户数据库
        await this.buildUserDatabase(userRegisteredEvents, invitationEvents);

        this.isInitialized = true;
        console.log(`数据库初始化完成，共 ${this.database.size} 个用户`);
    }
    
    // ========== 数据库构建函数 ==========

    private async getAllUserRegisteredEvents(fromBlock: number): Promise<any[]> {
        const filter = {
            address: this.registryAddress,
            topics: [this.getEventTopic('UserRegistered')],
            fromBlock: `0x${fromBlock.toString(16)}`,
            toBlock: 'latest'
        };

        return await this.alchemy.core.getLogs(filter);
    }

    private async getAllInvitationConfirmedEvents(fromBlock: number): Promise<any[]> {
        const filter = {
            address: this.registryAddress,
            topics: [this.getEventTopic('InvitationConfirmed')],
            fromBlock: `0x${fromBlock.toString(16)}`,
            toBlock: 'latest'
        };

        return await this.alchemy.core.getLogs(filter);
    }

    private async buildUserDatabase(userEvents: any[], invitationEvents: any[]): Promise<void> {
        // 处理用户注册事件
        for (const event of userEvents) {
            const decoded = this.decodeUserRegisteredEvent(event);
            const userInfo: UserInfo = {
                address: decoded.user,
                isRegistered: true,
                vault: decoded.vault,
                inviter: decoded.inviter !== '******************************************' ? decoded.inviter : undefined,
                invitedUsers: [],
                totalInvites: 0,
                dailyInvites: 0,
                lastInviteTimestamp: 0,
                registrationTimestamp: decoded.timestamp,
                invitationChain: []
            };

            this.database.set(decoded.user.toLowerCase(), userInfo);
        }

        // 处理邀请确认事件
        for (const event of invitationEvents) {
            const decoded = this.decodeInvitationConfirmedEvent(event);
            const inviterInfo = this.database.get(decoded.inviter.toLowerCase());
            if (inviterInfo) {
                inviterInfo.totalInvites = decoded.totalInvites;
                if (!inviterInfo.invitedUsers) inviterInfo.invitedUsers = [];
                // 注意：这里需要从事件中获取被邀请人地址，当前事件结构可能需要调整
            }
        }

        // 构建邀请链
        for (const [address, userInfo] of this.database) {
            userInfo.invitationChain = await this.buildInvitationChain(address);
        }
    }

    private async buildInvitationChain(userAddress: string): Promise<string[]> {
        const chain: string[] = [userAddress];
        let current = userAddress;

        while (chain.length < 100) {
            const userInfo = this.database.get(current.toLowerCase());
            if (!userInfo || !userInfo.inviter) break;

            chain.push(userInfo.inviter);
            current = userInfo.inviter;
        }

        return chain;
    }

    // ========== 替代链上查询函数 ==========

    /**
     * 检查用户是否已注册（替代链上 isRegistered 函数）
     */
    async isUserRegistered(userAddress: string): Promise<boolean> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const userInfo = this.database.get(userAddress.toLowerCase());
        return userInfo ? userInfo.isRegistered : false;
    }
    
    /**
     * 获取用户的Vault地址（替代直接调用 userVaults）
     */
    async getUserVault(userAddress: string): Promise<string> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const userInfo = this.database.get(userAddress.toLowerCase());
        return userInfo?.vault || '******************************************';
    }

    /**
     * 获取用户的邀请人（替代 inviterOf mapping）
     */
    async getInviterOf(userAddress: string): Promise<string> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const userInfo = this.database.get(userAddress.toLowerCase());
        return userInfo?.inviter || '******************************************';
    }

    /**
     * 获取完整的用户信息（数据库增强版本）
     */
    async getUserInfo(userAddress: string): Promise<UserInfo | null> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const userInfo = this.database.get(userAddress.toLowerCase());
        if (!userInfo) return null;

        // 实时更新统计信息
        if (userInfo.vault) {
            const stats = await this.getUserStatsFromVault(userInfo.vault);
            userInfo.totalInvites = stats.totalInvites;
            userInfo.dailyInvites = stats.dailyInvites;
            userInfo.lastInviteTimestamp = stats.lastInviteTimestamp;
        }

        return userInfo;
    }
    
    /**
     * 获取用户邀请的所有人（替代链上 getInvitedUsers 函数）
     */
    async getInvitedUsers(inviterAddress: string): Promise<string[]> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const invitedUsers: string[] = [];

        // 遍历数据库找到所有被该用户邀请的人
        for (const [address, userInfo] of this.database) {
            if (userInfo.inviter?.toLowerCase() === inviterAddress.toLowerCase()) {
                invitedUsers.push(address);
            }
        }

        return invitedUsers;
    }

    /**
     * 获取用户邀请的详细信息（增强版本）
     */
    async getInvitedUsersDetailed(inviterAddress: string): Promise<Array<{
        address: string;
        registrationTimestamp: number;
        vault: string;
        totalInvites: number;
    }>> {
        const invitedAddresses = await this.getInvitedUsers(inviterAddress);
        const detailedInfo = [];

        for (const address of invitedAddresses) {
            const userInfo = await this.getUserInfo(address);
            if (userInfo) {
                detailedInfo.push({
                    address: userInfo.address,
                    registrationTimestamp: userInfo.registrationTimestamp || 0,
                    vault: userInfo.vault || '',
                    totalInvites: userInfo.totalInvites
                });
            }
        }

        return detailedInfo.sort((a, b) => b.registrationTimestamp - a.registrationTimestamp);
    }
    
    /**
     * 构建完整的邀请链（替代链上 getInvitationChain 函数）
     */
    async getInvitationChain(userAddress: string): Promise<string[]> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const userInfo = this.database.get(userAddress.toLowerCase());
        return userInfo?.invitationChain || [userAddress];
    }

    /**
     * 获取邀请链的详细信息（增强版本）
     */
    async getInvitationChainDetailed(userAddress: string): Promise<Array<{
        address: string;
        registrationTimestamp: number;
        totalInvites: number;
        level: number;
    }>> {
        const chain = await this.getInvitationChain(userAddress);
        const detailedChain = [];

        for (let i = 0; i < chain.length; i++) {
            const userInfo = await this.getUserInfo(chain[i]);
            if (userInfo) {
                detailedChain.push({
                    address: userInfo.address,
                    registrationTimestamp: userInfo.registrationTimestamp || 0,
                    totalInvites: userInfo.totalInvites,
                    level: i
                });
            }
        }

        return detailedChain;
    }
    
    /**
     * 检查是否可以邀请某用户（链下版本，仅供参考）
     * 注意：实际验证仍需调用链上的 canInvite 函数确保安全
     */
    async canInviteOffchain(inviterAddress: string, inviteeAddress: string): Promise<{
        canInvite: boolean;
        reason?: string;
    }> {
        // 基本检查
        if (inviterAddress === inviteeAddress) {
            return { canInvite: false, reason: 'Cannot self-invite' };
        }

        // 检查邀请人是否已注册
        const inviterRegistered = await this.isUserRegistered(inviterAddress);
        if (!inviterRegistered) {
            return { canInvite: false, reason: 'Inviter not registered' };
        }

        // 检查被邀请人是否已注册
        const inviteeRegistered = await this.isUserRegistered(inviteeAddress);
        if (inviteeRegistered) {
            return { canInvite: false, reason: 'Invitee already registered' };
        }

        // 检查是否已经邀请过
        const inviterVault = await this.getUserVault(inviterAddress);
        if (inviterVault !== '******************************************') {
            const hasInvited = await this.hasInvitedUser(inviterVault, inviteeAddress);
            if (hasInvited) {
                return { canInvite: false, reason: 'Already invited this user' };
            }
        }

        return { canInvite: true };
    }

    /**
     * 预计算Vault地址（复用链上逻辑）
     */
    async computeVaultAddress(userAddress: string): Promise<string> {
        const salt = ethers.utils.keccak256(
            ethers.utils.defaultAbiCoder.encode(['address'], [userAddress])
        );

        // 使用 CREATE2 公式计算
        const initCodeHash = await this.getVaultInitCodeHash();
        return ethers.utils.getCreate2Address(
            this.registryAddress,  // deployer
            salt,                  // salt
            initCodeHash          // initCodeHash
        );
    }

    private async getVaultInitCodeHash(): Promise<string> {
        // 这里需要获取 UserVault 的 initCodeHash
        // 实际实现中可能需要从合约中读取或预先计算
        return '0x' + '0'.repeat(64); // 占位符，实际需要正确的 hash
    }
    
    /**
     * 检查UserVault中是否已邀请过某用户
     */
    private async hasInvitedUser(vaultAddress: string, userAddress: string): Promise<boolean> {
        const storageSlot = this.getStorageSlot('hasInvited', userAddress, vaultAddress);
        const result = await this.alchemy.core.getStorageAt(vaultAddress, storageSlot);
        return result !== '******************************************000000000000000000000000';
    }
    
    /**
     * 从UserVault获取用户统计信息
     */
    private async getUserStatsFromVault(vaultAddress: string): Promise<{
        totalInvites: number;
        dailyInvites: number;
        lastInviteTimestamp: number;
    }> {
        if (vaultAddress === '******************************************') {
            return { totalInvites: 0, dailyInvites: 0, lastInviteTimestamp: 0 };
        }

        // 读取UserVault的存储槽
        const totalInvitesSlot = '******************************************000000000000000000000004';
        const dailyInvitesSlot = '******************************************000000000000000000000005';
        const timestampSlot = '******************************************000000000000000000000006';

        const [totalInvites, dailyInvites, timestamp] = await Promise.all([
            this.alchemy.core.getStorageAt(vaultAddress, totalInvitesSlot),
            this.alchemy.core.getStorageAt(vaultAddress, dailyInvitesSlot),
            this.alchemy.core.getStorageAt(vaultAddress, timestampSlot)
        ]);

        return {
            totalInvites: parseInt(totalInvites, 16),
            dailyInvites: parseInt(dailyInvites, 16),
            lastInviteTimestamp: parseInt(timestamp, 16)
        };
    }

    /**
     * 获取当前每日邀请数（替代链上 getCurrentDailyInvites 函数）
     */
    async getCurrentDailyInvites(userAddress: string): Promise<number> {
        const userInfo = await this.getUserInfo(userAddress);
        if (!userInfo || !userInfo.vault) return 0;

        const stats = await this.getUserStatsFromVault(userInfo.vault);
        const currentTime = Math.floor(Date.now() / 1000);

        // 如果超过一天，返回0
        if (currentTime - stats.lastInviteTimestamp >= 86400) {
            return 0;
        }

        return stats.dailyInvites;
    }
    
    // ========== 工具函数 ==========
    
    private getStorageSlot(mappingName: string, key: string, contractAddress?: string): string {
        // 计算mapping存储槽位置
        const mappingSlot = this.getMappingSlot(mappingName);
        const keyHash = ethers.utils.keccak256(
            ethers.utils.defaultAbiCoder.encode(['address', 'uint256'], [key, mappingSlot])
        );
        return keyHash;
    }
    
    private getMappingSlot(mappingName: string): number {
        // 根据合约存储布局返回mapping的槽位置
        const slots: Record<string, number> = {
            'userVaults': 0,
            'hasInvited': 5  // UserVault中的hasInvited mapping
        };
        return slots[mappingName] || 0;
    }
    
    private getEventTopic(eventName: string): string {
        const eventSignatures: Record<string, string> = {
            'UserRegistered': 'UserRegistered(address,address,address,uint256)',
            'InvitationConfirmed': 'InvitationConfirmed(address,address,uint256)'
        };
        
        return ethers.utils.id(eventSignatures[eventName]);
    }
    
    private decodeUserRegisteredEvent(log: any): {
        user: string;
        inviter: string;
        vault: string;
        timestamp: number;
    } {
        const iface = new ethers.utils.Interface([
            'event UserRegistered(address indexed user, address indexed inviter, address vault, uint256 timestamp)'
        ]);

        const decoded = iface.parseLog(log);
        return {
            user: decoded.args.user,
            inviter: decoded.args.inviter,
            vault: decoded.args.vault,
            timestamp: decoded.args.timestamp.toNumber()
        };
    }

    private decodeInvitationConfirmedEvent(log: any): {
        inviter: string;
        invitee: string;
        totalInvites: number;
    } {
        const iface = new ethers.utils.Interface([
            'event InvitationConfirmed(address indexed inviter, address indexed invitee, uint32 totalInvites)'
        ]);

        const decoded = iface.parseLog(log);
        return {
            inviter: decoded.args.inviter,
            invitee: decoded.args.invitee,
            totalInvites: decoded.args.totalInvites
        };
    }
    
    // ========== 增强的统计分析功能 ==========

    /**
     * 获取系统总体统计信息
     */
    async getSystemStats(): Promise<InvitationStats> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const totalUsers = this.database.size;
        let totalInvitations = 0;
        let activeInviters = 0;
        const inviterStats: Map<string, number> = new Map();

        for (const [address, userInfo] of this.database) {
            totalInvitations += userInfo.totalInvites;
            if (userInfo.totalInvites > 0) {
                activeInviters++;
                inviterStats.set(address, userInfo.totalInvites);
            }
        }

        // 获取Top邀请者
        const topInviters = Array.from(inviterStats.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([address, totalInvites]) => ({ address, totalInvites }));

        return {
            totalUsers,
            totalInvitations,
            activeInviters,
            averageInvitesPerUser: totalUsers > 0 ? totalInvitations / totalUsers : 0,
            topInviters
        };
    }

    /**
     * 获取每日统计信息
     */
    async getDailyStats(days: number = 30): Promise<DailyStats[]> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const dailyStats: Map<string, DailyStats> = new Map();
        const now = new Date();

        // 初始化日期
        for (let i = 0; i < days; i++) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            dailyStats.set(dateStr, {
                date: dateStr,
                newRegistrations: 0,
                totalInvitations: 0,
                activeUsers: 0
            });
        }

        // 统计注册数据
        for (const [address, userInfo] of this.database) {
            if (userInfo.registrationTimestamp) {
                const date = new Date(userInfo.registrationTimestamp * 1000);
                const dateStr = date.toISOString().split('T')[0];

                const stats = dailyStats.get(dateStr);
                if (stats) {
                    stats.newRegistrations++;
                }
            }
        }

        return Array.from(dailyStats.values()).sort((a, b) =>
            new Date(b.date).getTime() - new Date(a.date).getTime()
        );
    }

    /**
     * 批量查询用户信息（增强版本）
     */
    async batchQuery(userAddresses: string[]): Promise<UserInfo[]> {
        if (!this.isInitialized) {
            await this.initializeDatabase();
        }

        const results: UserInfo[] = [];

        for (const address of userAddresses) {
            const userInfo = await this.getUserInfo(address);
            if (userInfo) {
                results.push(userInfo);
            } else {
                // 未注册用户
                results.push({
                    address: address.toLowerCase(),
                    isRegistered: false,
                    totalInvites: 0,
                    dailyInvites: 0,
                    lastInviteTimestamp: 0
                });
            }
        }

        return results;
    }
    
    // ========== 数据库管理功能 ==========

    /**
     * 增量更新数据库（处理新事件）
     */
    async updateDatabase(fromBlock: number): Promise<void> {
        console.log(`正在更新数据库，从区块 ${fromBlock} 开始...`);

        const [userEvents, invitationEvents] = await Promise.all([
            this.getAllUserRegisteredEvents(fromBlock),
            this.getAllInvitationConfirmedEvents(fromBlock)
        ]);

        // 处理新的用户注册事件
        for (const event of userEvents) {
            const decoded = this.decodeUserRegisteredEvent(event);
            if (!this.database.has(decoded.user.toLowerCase())) {
                const userInfo: UserInfo = {
                    address: decoded.user,
                    isRegistered: true,
                    vault: decoded.vault,
                    inviter: decoded.inviter !== '******************************************' ? decoded.inviter : undefined,
                    invitedUsers: [],
                    totalInvites: 0,
                    dailyInvites: 0,
                    lastInviteTimestamp: 0,
                    registrationTimestamp: decoded.timestamp,
                    invitationChain: await this.buildInvitationChain(decoded.user)
                };

                this.database.set(decoded.user.toLowerCase(), userInfo);
            }
        }

        console.log(`数据库更新完成，新增 ${userEvents.length} 个用户`);
    }

    /**
     * 导出数据库为JSON
     */
    exportDatabase(): string {
        const data = {
            timestamp: Date.now(),
            totalUsers: this.database.size,
            users: Array.from(this.database.entries()).map(([address, userInfo]) => ({
                address,
                ...userInfo
            }))
        };

        return JSON.stringify(data, null, 2);
    }

    /**
     * 从JSON导入数据库
     */
    importDatabase(jsonData: string): void {
        const data = JSON.parse(jsonData);
        this.database.clear();

        for (const userData of data.users) {
            const { address, ...userInfo } = userData;
            this.database.set(address, userInfo);
        }

        this.isInitialized = true;
        console.log(`数据库导入完成，共 ${this.database.size} 个用户`);
    }

    /**
     * 获取数据库统计信息
     */
    getDatabaseInfo(): {
        isInitialized: boolean;
        totalUsers: number;
        cacheSize: number;
        lastUpdate: number;
    } {
        return {
            isInitialized: this.isInitialized,
            totalUsers: this.database.size,
            cacheSize: this.cache.size,
            lastUpdate: Date.now()
        };
    }

    /**
     * 清除缓存
     */
    clearCache(): void {
        this.cache.clear();
    }

    /**
     * 清除数据库
     */
    clearDatabase(): void {
        this.database.clear();
        this.isInitialized = false;
    }
}

// ========== 使用示例和工厂函数 ==========

/**
 * 创建完整的Alchemy查询服务实例
 */
export async function createAlchemyQueryService(
    apiKey: string,
    registryAddress: string,
    vaultImplementationAddress: string,
    network: Network = Network.ETH_MAINNET
): Promise<AlchemyInvitationQueryService> {
    const service = new AlchemyInvitationQueryService(
        apiKey,
        registryAddress,
        vaultImplementationAddress,
        network
    );

    // 初始化数据库
    await service.initializeDatabase();

    return service;
}

/**
 * 完整的使用示例
 */
export async function exampleUsage() {
    const apiKey = 'your-alchemy-api-key';
    const registryAddress = '******************************************';
    const vaultImplementationAddress = '******************************************';

    // 创建服务实例
    const service = await createAlchemyQueryService(
        apiKey,
        registryAddress,
        vaultImplementationAddress
    );

    const userAddress = '******************************************';
    const inviterAddress = '******************************************';

    console.log('=== 基础查询功能 ===');
    console.log('用户是否已注册:', await service.isUserRegistered(userAddress));
    console.log('用户Vault地址:', await service.getUserVault(userAddress));
    console.log('用户邀请人:', await service.getInviterOf(userAddress));
    console.log('用户邀请的人:', await service.getInvitedUsers(userAddress));
    console.log('邀请链条:', await service.getInvitationChain(userAddress));
    console.log('当前每日邀请数:', await service.getCurrentDailyInvites(userAddress));

    console.log('=== 增强查询功能 ===');
    console.log('完整用户信息:', await service.getUserInfo(userAddress));
    console.log('邀请用户详情:', await service.getInvitedUsersDetailed(inviterAddress));
    console.log('邀请链详情:', await service.getInvitationChainDetailed(userAddress));
    console.log('预计算Vault地址:', await service.computeVaultAddress(userAddress));

    console.log('=== 统计分析功能 ===');
    console.log('系统统计:', await service.getSystemStats());
    console.log('每日统计:', await service.getDailyStats(7));

    console.log('=== 批量查询功能 ===');
    const addresses = [userAddress, inviterAddress];
    console.log('批量查询:', await service.batchQuery(addresses));

    console.log('=== 数据库管理 ===');
    console.log('数据库信息:', service.getDatabaseInfo());

    // 导出数据库
    const exportedData = service.exportDatabase();
    console.log('数据库已导出，大小:', exportedData.length, '字符');

    return service;
}

/**
 * 快速查询工具函数（无需初始化完整数据库）
 */
export class QuickAlchemyQuery {
    private alchemy: Alchemy;
    private registryAddress: string;

    constructor(apiKey: string, registryAddress: string, network: Network = Network.ETH_MAINNET) {
        this.alchemy = new Alchemy({ apiKey, network });
        this.registryAddress = registryAddress;
    }

    /**
     * 快速检查用户是否已注册
     */
    async isRegistered(userAddress: string): Promise<boolean> {
        const storageSlot = ethers.utils.keccak256(
            ethers.utils.defaultAbiCoder.encode(['address', 'uint256'], [userAddress, 0])
        );
        const vault = await this.alchemy.core.getStorageAt(this.registryAddress, storageSlot);
        return vault !== '******************************************000000000000000000000000';
    }

    /**
     * 快速获取用户Vault地址
     */
    async getUserVault(userAddress: string): Promise<string> {
        const storageSlot = ethers.utils.keccak256(
            ethers.utils.defaultAbiCoder.encode(['address', 'uint256'], [userAddress, 0])
        );
        const vault = await this.alchemy.core.getStorageAt(this.registryAddress, storageSlot);

        if (vault === '******************************************000000000000000000000000') {
            return '******************************************';
        }

        return '0x' + vault.slice(-40);
    }
}
