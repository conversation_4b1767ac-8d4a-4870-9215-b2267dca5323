import { ethers } from 'ethers';

/**
 * 高效的邀请关系分析工具
 */
export class InvitationAnalytics {
    private provider: ethers.Provider;
    private contract: ethers.Contract;
    
    // 缓存邀请关系
    private invitationGraph: Map<string, Set<string>> = new Map();
    private reverseGraph: Map<string, string> = new Map();  // user => inviter
    
    constructor(contractAddress: string, provider: ethers.Provider) {
        this.provider = provider;
        this.contract = new ethers.Contract(contractAddress, ABI, provider);
    }
    
    /**
     * 构建完整的邀请关系图（一次性读取所有事件）
     */
    async buildInvitationGraph() {
        console.log('Building invitation graph from events...');
        
        // 获取所有 UserJoined 事件
        const filter = this.contract.filters.UserJoined();
        const events = await this.contract.queryFilter(filter, 0, 'latest');
        
        // 处理每个事件
        for (const event of events) {
            const { user, inviter } = event.args;
            const userAddr = user.toLowerCase();
            const inviterAddr = inviter.toLowerCase();
            
            // 记录正向关系（邀请人 -> 被邀请人集合）
            if (!this.invitationGraph.has(inviterAddr)) {
                this.invitationGraph.set(inviterAddr, new Set());
            }
            this.invitationGraph.get(inviterAddr)!.add(userAddr);
            
            // 记录反向关系（被邀请人 -> 邀请人）
            this.reverseGraph.set(userAddr, inviterAddr);
        }
        
        console.log(`Graph built: ${this.reverseGraph.size} users indexed`);
    }
    
    /**
     * 获取用户的完整统计
     */
    getUserStats(address: string): {
        directInvites: string[];
        indirectInvites: string[];
        totalDirectCount: number;
        totalIndirectCount: number;
    } {
        const addr = address.toLowerCase();
        
        // 直接邀请的人
        const directInvites = Array.from(this.invitationGraph.get(addr) || []);
        
        // 间接邀请的人（我的直接邀请人所邀请的其他人）
        const indirectInvites: string[] = [];
        for (const directInvitee of directInvites) {
            const grandChildren = this.invitationGraph.get(directInvitee) || [];
            indirectInvites.push(...Array.from(grandChildren));
        }
        
        return {
            directInvites,
            indirectInvites,
            totalDirectCount: directInvites.length,
            totalIndirectCount: indirectInvites.length
        };
    }
    
    /**
     * 获取用户的邀请链
     */
    getInvitationChain(address: string): string[] {
        const chain: string[] = [address.toLowerCase()];
        let current = address.toLowerCase();
        
        // 向上追溯
        while (this.reverseGraph.has(current)) {
            const inviter = this.reverseGraph.get(current)!;
            chain.unshift(inviter);
            current = inviter;
        }
        
        return chain;
    }
    
    /**
     * 导出数据用于空投
     */
    async exportForAirdrop(): Promise<Array<{
        user: string;
        inviter: string | null;
        grandInviter: string | null;
        directInviteCount: number;
        indirectInviteCount: number;
    }>> {
        await this.buildInvitationGraph();
        
        const results = [];
        
        for (const [user, inviter] of this.reverseGraph.entries()) {
            const grandInviter = this.reverseGraph.get(inviter) || null;
            const stats = this.getUserStats(user);
            
            results.push({
                user,
                inviter,
                grandInviter,
                directInviteCount: stats.totalDirectCount,
                indirectInviteCount: stats.totalIndirectCount
            });
        }
        
        return results;
    }
}

// 使用示例
async function analyzeInvitations() {
    const analytics = new InvitationAnalytics(CONTRACT_ADDRESS, provider);
    
    // 构建关系图
    await analytics.buildInvitationGraph();
    
    // 查询特定用户统计
    const aliceStats = analytics.getUserStats('0xAlice');
    console.log(`Alice directly invited: ${aliceStats.totalDirectCount} users`);
    console.log(`Alice indirectly invited: ${aliceStats.totalIndirectCount} users`);
    
    // 查看邀请链
    const charlieChain = analytics.getInvitationChain('0xCharlie');
    console.log(`Charlie's invitation chain: ${charlieChain.join(' -> ')}`);
    
    // 导出空投数据
    const airdropData = await analytics.exportForAirdrop();
    fs.writeFileSync('airdrop_data.json', JSON.stringify(airdropData, null, 2));
}