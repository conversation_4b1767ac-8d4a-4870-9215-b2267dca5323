import { ethers } from "hardhat";

async function main() {
    console.log("Deploying Invitation System - Original Version (Immutable)...");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);

    // 1. 部署Registry（会自动部署Vault实现）
    const Registry = await ethers.getContractFactory("InvitationRegistry");
    const registry = await Registry.deploy(deployer.address); // deployer作为根用户
    await registry.waitForDeployment();
    
    const registryAddress = await registry.getAddress();
    console.log("Registry deployed to:", registryAddress);
    
    // 2. 获取Vault实现地址
    const vaultImpl = await registry.vaultImplementation();
    console.log("Vault implementation:", vaultImpl);
    
    // 3. 验证根用户
    const rootUser = await registry.rootUser();
    const rootVault = await registry.userVaults(rootUser);
    console.log("Root user:", rootUser);
    console.log("Root vault:", rootVault);
    
    // 4. 保存部署信息
    const deployment = {
        network: hre.network.name,
        registry: registryAddress,
        vaultImplementation: vaultImpl,
        rootUser: rootUser,
        deployedAt: new Date().toISOString()
    };
    
    const fs = require("fs");
    fs.writeFileSync(
        `deployments/${hre.network.name}.json`,
        JSON.stringify(deployment, null, 2)
    );
    
    console.log("\nDeployment completed!");
    console.log("Run verification with:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${registryAddress} ${rootUser}`);
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });