import { ethers } from 'ethers';
import fs from 'fs';

/**
 * 高效的空投脚本
 */
class AirdropManager {
    private provider: ethers.Provider;
    private contract: ethers.Contract;
    private signer: ethers.Signer;
    
    constructor(
        contractAddress: string, 
        provider: ethers.Provider,
        signer: ethers.Signer
    ) {
        this.provider = provider;
        this.signer = signer;
        this.contract = new ethers.Contract(contractAddress, ABI, signer);
    }
    
    /**
     * 方案1：直接从事件构建空投列表
     */
    async prepareAirdropFromEvents(
        tokenAddress: string,
        baseAmount: number
    ): Promise<{
        recipients: string[];
        amounts: number[];
        totalAmount: number;
    }> {
        // 获取所有加入事件
        const events = await this.contract.queryFilter('UserJoined', 0, 'latest');
        
        // 构建邀请关系
        const inviterMap = new Map<string, string>();
        const inviteCountMap = new Map<string, number>();
        
        for (const event of events) {
            const { user, inviter } = event.args;
            inviterMap.set(user, inviter);
            
            const count = inviteCountMap.get(inviter) || 0;
            inviteCountMap.set(inviter, count + 1);
        }
        
        // 计算每个人的空投金额
        const recipients: string[] = [];
        const amounts: number[] = [];
        
        for (const [user] of inviterMap) {
            const directInvites = inviteCountMap.get(user) || 0;
            
            // 基础金额 + 邀请奖励
            const amount = baseAmount + (directInvites * baseAmount * 0.1);
            
            recipients.push(user);
            amounts.push(amount);
        }
        
        const totalAmount = amounts.reduce((a, b) => a + b, 0);
        
        return { recipients, amounts, totalAmount };
    }
    
    /**
     * 方案2：批量查询链上数据
     */
    async prepareAirdropFromChain(
        userAddresses: string[],
        baseAmount: number
    ) {
        // 批量查询邀请人
        const inviters = await this.contract.batchGetInviters(userAddresses);
        
        // 构建空投数据
        const airdropData = userAddresses.map((user, i) => {
            const inviter = inviters[i];
            const grandInviter = inviter !== ethers.ZeroAddress 
                ? (await this.contract.inviterOf(inviter))
                : ethers.ZeroAddress;
                
            return {
                user,
                inviter,
                grandInviter,
                // 根据邀请关系计算奖励...
            };
        });
        
        return airdropData;
    }
    
    /**
     * 执行空投（支持邀请奖励）
     */
    async executeAirdrop(
        recipients: string[],
        amounts: number[],
        includeReferralRewards: boolean = true
    ) {
        console.log(`Preparing to airdrop to ${recipients.length} users...`);
        
        // 如果包含邀请奖励，使用合约的批量空投功能
        if (includeReferralRewards) {
            // 合约会自动计算并分配邀请奖励
            const tx = await this.contract.batchAirdropWithRewards(
                recipients,
                amounts
            );
            
            console.log(`Transaction sent: ${tx.hash}`);
            await tx.wait();
            console.log('Airdrop completed with referral rewards!');
            
        } else {
            // 直接转账，不计算邀请奖励
            // 分批执行避免gas limit
            const batchSize = 100;
            
            for (let i = 0; i < recipients.length; i += batchSize) {
                const batch = recipients.slice(i, i + batchSize);
                const batchAmounts = amounts.slice(i, i + batchSize);
                
                const tx = await this.contract.batchTransfer(batch, batchAmounts);
                await tx.wait();
                
                console.log(`Batch ${i / batchSize + 1} completed`);
            }
        }
    }
    
    /**
     * 生成空投报告
     */
    async generateAirdropReport() {
        const analytics = new InvitationAnalytics(this.contract.address, this.provider);
        await analytics.buildInvitationGraph();
        
        const data = await analytics.exportForAirdrop();
        
        // 生成CSV报告
        const csv = [
            'User,Direct Inviter,Grand Inviter,Direct Invites,Indirect Invites,Suggested Amount',
            ...data.map(row => {
                const suggestedAmount = 100 + (row.directInviteCount * 10) + (row.indirectInviteCount * 5);
                return `${row.user},${row.inviter || 'None'},${row.grandInviter || 'None'},${row.directInviteCount},${row.indirectInviteCount},${suggestedAmount}`;
            })
        ].join('\n');
        
        fs.writeFileSync('airdrop_report.csv', csv);
        console.log('Report saved to airdrop_report.csv');
    }
}

// 使用示例
async function runAirdrop() {
    const manager = new AirdropManager(CONTRACT_ADDRESS, provider, signer);
    
    // 1. 准备空投数据
    const { recipients, amounts, totalAmount } = await manager.prepareAirdropFromEvents(
        TOKEN_ADDRESS,
        100 // 基础空投金额
    );
    
    console.log(`Total recipients: ${recipients.length}`);
    console.log(`Total amount needed: ${totalAmount}`);
    
    // 2. 执行空投
    await manager.executeAirdrop(recipients, amounts, true);
    
    // 3. 生成报告
    await manager.generateAirdropReport();
}

// 快速统计脚本
async function quickStats() {
    const contract = new ethers.Contract(CONTRACT_ADDRESS, ABI, provider);
    
    // 使用事件快速统计
    const events = await contract.queryFilter('UserJoined');
    
    const stats = new Map<string, { direct: number, indirect: number }>();
    
    events.forEach(event => {
        const { user, inviter } = event.args;
        
        // 更新直接邀请统计
        if (!stats.has(inviter)) {
            stats.set(inviter, { direct: 0, indirect: 0 });
        }
        stats.get(inviter)!.direct++;
        
        // 更新间接邀请统计
        const grandInviter = events.find(e => e.args.user === inviter)?.args.inviter;
        if (grandInviter && !stats.has(grandInviter)) {
            stats.set(grandInviter, { direct: 0, indirect: 0 });
        }
        if (grandInviter) {
            stats.get(grandInviter)!.indirect++;
        }
    });
    
    // 输出TOP 10
    const sorted = Array.from(stats.entries())
        .sort((a, b) => (b[1].direct + b[1].indirect) - (a[1].direct + a[1].indirect))
        .slice(0, 10);
        
    console.log('Top 10 Inviters:');
    sorted.forEach(([address, stats], i) => {
        console.log(`${i + 1}. ${address}: ${stats.direct} direct, ${stats.indirect} indirect`);
    });
}