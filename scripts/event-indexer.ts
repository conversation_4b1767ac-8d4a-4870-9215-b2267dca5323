import { ethers } from 'ethers';

/**
 * 事件索引服务 - 用于构建链下邀请关系数据库
 * 这是推荐的Web3 dApp设计模式：链上存储核心数据，链下索引详细信息
 */
export class InvitationEventIndexer {
    private provider: ethers.Provider;
    private contract: ethers.Contract;
    private database: Map<string, any> = new Map(); // 模拟数据库
    
    constructor(contractAddress: string, provider: ethers.Provider) {
        this.provider = provider;
        this.contract = new ethers.Contract(contractAddress, ABI, provider);
    }
    
    /**
     * 初始化索引 - 从创世块开始索引所有事件
     */
    async initializeIndex(fromBlock: number = 0) {
        console.log('开始构建邀请关系索引...');
        
        // 1. 索引用户注册事件
        await this.indexUserRegistrations(fromBlock);
        
        // 2. 索引邀请确认事件
        await this.indexInvitationConfirmations(fromBlock);
        
        console.log('索引构建完成');
    }
    
    /**
     * 索引用户注册事件
     */
    private async indexUserRegistrations(fromBlock: number) {
        const filter = this.contract.filters.UserRegistered();
        const events = await this.contract.queryFilter(filter, fromBlock);
        
        for (const event of events) {
            const [user, inviter, vault, timestamp] = event.args || [];
            
            this.database.set(`user:${user}`, {
                address: user,
                inviter,
                vault,
                registrationTime: new Date(Number(timestamp) * 1000),
                totalInvites: 0,
                invitedUsers: [],
                invitationChain: await this.buildInvitationChain(user)
            });
        }
        
        console.log(`索引了 ${events.length} 个用户注册事件`);
    }
    
    /**
     * 索引邀请确认事件
     */
    private async indexInvitationConfirmations(fromBlock: number) {
        const filter = this.contract.filters.InvitationConfirmed();
        const events = await this.contract.queryFilter(filter, fromBlock);
        
        for (const event of events) {
            const [inviter, invitee, totalInvites] = event.args || [];
            const blockInfo = await event.getBlock();
            
            // 更新邀请人信息
            const inviterData = this.database.get(`user:${inviter}`) || {};
            inviterData.totalInvites = Number(totalInvites);
            inviterData.invitedUsers = inviterData.invitedUsers || [];
            
            if (!inviterData.invitedUsers.includes(invitee)) {
                inviterData.invitedUsers.push(invitee);
            }
            
            this.database.set(`user:${inviter}`, inviterData);
            
            // 记录邀请关系
            this.database.set(`invitation:${inviter}:${invitee}`, {
                inviter,
                invitee,
                timestamp: new Date(blockInfo.timestamp * 1000),
                blockNumber: event.blockNumber,
                transactionHash: event.transactionHash
            });
        }
        
        console.log(`索引了 ${events.length} 个邀请确认事件`);
    }
    
    /**
     * 构建邀请链条
     */
    private async buildInvitationChain(user: string): Promise<string[]> {
        const chain: string[] = [user];
        let current = user;
        
        while (true) {
            const inviter = await this.contract.inviterOf(current);
            if (inviter === ethers.ZeroAddress) break;
            
            chain.push(inviter);
            current = inviter;
            
            // 防止无限循环
            if (chain.length > 100) break;
        }
        
        return chain;
    }
    
    /**
     * 实时监听新事件
     */
    startRealTimeIndexing() {
        // 监听用户注册
        this.contract.on('UserRegistered', async (user, inviter, vault, timestamp, event) => {
            console.log(`新用户注册: ${user}`);
            
            this.database.set(`user:${user}`, {
                address: user,
                inviter,
                vault,
                registrationTime: new Date(Number(timestamp) * 1000),
                totalInvites: 0,
                invitedUsers: [],
                invitationChain: await this.buildInvitationChain(user)
            });
        });
        
        // 监听邀请确认
        this.contract.on('InvitationConfirmed', async (inviter, invitee, totalInvites, event) => {
            console.log(`新邀请确认: ${inviter} -> ${invitee}`);
            
            const blockInfo = await event.getBlock();
            
            // 更新邀请人数据
            const inviterData = this.database.get(`user:${inviter}`) || {};
            inviterData.totalInvites = Number(totalInvites);
            inviterData.invitedUsers = inviterData.invitedUsers || [];
            
            if (!inviterData.invitedUsers.includes(invitee)) {
                inviterData.invitedUsers.push(invitee);
            }
            
            this.database.set(`user:${inviter}`, inviterData);
            
            // 记录邀请关系
            this.database.set(`invitation:${inviter}:${invitee}`, {
                inviter,
                invitee,
                timestamp: new Date(blockInfo.timestamp * 1000),
                blockNumber: event.blockNumber,
                transactionHash: event.transactionHash
            });
        });
        
        console.log('开始实时索引事件...');
    }
    
    /**
     * 查询用户完整信息（从索引）
     */
    getUserInfo(userAddress: string) {
        return this.database.get(`user:${userAddress}`);
    }
    
    /**
     * 获取用户邀请的所有人
     */
    getInvitedUsers(inviterAddress: string): string[] {
        const userData = this.database.get(`user:${inviterAddress}`);
        return userData?.invitedUsers || [];
    }
    
    /**
     * 获取邀请关系详情
     */
    getInvitationDetails(inviter: string, invitee: string) {
        return this.database.get(`invitation:${inviter}:${invitee}`);
    }
    
    /**
     * 分析邀请数据（用于空投分配）
     */
    analyzeInvitationData() {
        const users = Array.from(this.database.keys())
            .filter(key => key.startsWith('user:'))
            .map(key => this.database.get(key));
        
        // 按邀请数量排序
        const topInviters = users
            .sort((a, b) => b.totalInvites - a.totalInvites)
            .slice(0, 100);
        
        // 计算邀请深度分布
        const depthDistribution = users.reduce((acc, user) => {
            const depth = user.invitationChain?.length || 0;
            acc[depth] = (acc[depth] || 0) + 1;
            return acc;
        }, {} as Record<number, number>);
        
        return {
            totalUsers: users.length,
            topInviters,
            depthDistribution,
            averageInvites: users.reduce((sum, user) => sum + user.totalInvites, 0) / users.length
        };
    }
    
    /**
     * 导出数据用于空投
     */
    exportForAirdrop() {
        const users = Array.from(this.database.keys())
            .filter(key => key.startsWith('user:'))
            .map(key => {
                const user = this.database.get(key);
                return {
                    address: user.address,
                    totalInvites: user.totalInvites,
                    invitationDepth: user.invitationChain?.length || 0,
                    registrationTime: user.registrationTime
                };
            });
        
        return users;
    }
}

// 合约ABI（简化版）
const ABI = [
    "event UserRegistered(address indexed user, address indexed inviter, address vault, uint256 timestamp)",
    "event InvitationConfirmed(address indexed inviter, address indexed invitee, uint256 totalInvites)",
    "function inviterOf(address user) view returns (address)",
    "function userVaults(address user) view returns (address)"
];

// 使用示例
export async function setupEventIndexer(contractAddress: string, provider: ethers.Provider) {
    const indexer = new InvitationEventIndexer(contractAddress, provider);
    
    // 初始化历史数据索引
    await indexer.initializeIndex();
    
    // 开始实时监听
    indexer.startRealTimeIndexing();
    
    return indexer;
}
