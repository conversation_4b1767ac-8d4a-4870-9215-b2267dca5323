import { ethers } from 'ethers';

/**
 * Invitation System SDK
 * 提供完整的前端集成工具
 */
export class InvitationSDK {
    private registry: ethers.Contract;
    private provider: ethers.Provider;
    
    constructor(registryAddress: string, provider: ethers.Provider) {
        this.provider = provider;
        this.registry = new ethers.Contract(registryAddress, REGISTRY_ABI, provider);
    }
    
    /**
     * 验证邀请链接
     */
    async verifyInvitation(inviterAddress: string, inviteeAddress: string): Promise<{
        valid: boolean;
        reason?: string;
        details?: any;
    }> {
        try {
            // 批量查询
            const [vaults, inviters, isRegistered] = await this.registry.batchQuery([
                inviterAddress,
                inviteeAddress
            ]);
            
            // 检查被邀请人是否已注册
            if (isRegistered[1]) {
                return { valid: false, reason: 'Already registered' };
            }
            
            // 检查邀请人是否有效
            const rootUser = await this.registry.rootUser();
            if (!isRegistered[0] && inviterAddress !== rootUser) {
                return { valid: false, reason: 'Invalid inviter' };
            }
            
            // 检查自我邀请
            if (inviterAddress === inviteeAddress) {
                return { valid: false, reason: 'Cannot self-invite' };
            }
            
            return {
                valid: true,
                details: {
                    inviterVault: vaults[0],
                    inviterAddress
                }
            };
            
        } catch (error) {
            return { valid: false, reason: error.message };
        }
    }
    
    /**
     * 注册新用户
     */
    async register(signer: ethers.Signer, inviterAddress: string): Promise<{
        success: boolean;
        vault?: string;
        tx?: ethers.TransactionResponse;
        error?: string;
    }> {
        try {
            const contract = this.registry.connect(signer);
            const tx = await contract.register(inviterAddress);
            const receipt = await tx.wait();
            
            // 从事件中获取Vault地址
            const event = receipt.logs.find(
                log => log.eventName === 'UserRegistered'
            );
            
            return {
                success: true,
                vault: event?.args?.vault,
                tx
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.reason || error.message
            };
        }
    }
    
    /**
     * 获取用户信息
     */
    async getUserInfo(address: string) {
        const [vault, inviter] = await Promise.all([
            this.registry.userVaults(address),
            this.registry.inviterOf(address)
        ]);

        if (vault === ethers.ZeroAddress) {
            return null;
        }

        const vaultContract = new ethers.Contract(vault, VAULT_ABI, this.provider);
        const [totalInvites, dailyInvites] = await Promise.all([
            vaultContract.totalInvites(),
            vaultContract.getCurrentDailyInvites()
        ]);

        // 从事件获取邀请用户列表（链下索引）
        const invitedUsers = await this.getInvitedUsersFromEvents(address);

        return {
            address,
            vault,
            inviter,
            totalInvites: totalInvites.toNumber(),
            dailyInvites: dailyInvites.toNumber(),
            invitedUsers
        };
    }

    /**
     * 从事件中获取用户邀请的所有人（链下索引）
     */
    async getInvitedUsersFromEvents(inviterAddress: string): Promise<string[]> {
        try {
            // 查询 InvitationConfirmed 事件
            const filter = this.registry.filters.InvitationConfirmed(inviterAddress);
            const events = await this.registry.queryFilter(filter);

            return events.map(event => event.args?.[1] || '').filter(addr => addr);
        } catch (error) {
            console.error('从事件获取邀请用户失败:', error);
            return [];
        }
    }
    
    /**
     * 生成邀请链接（多种格式）
     */
    generateInviteLink(address: string, baseUrl: string = 'https://app.com'): {
        full: string;
        short: string;
        qr: string;
    } {
        // 完整链接
        const full = `${baseUrl}/join?inviter=${address}`;
        
        // 短链接（Base58压缩）
        const compressed = this.compressAddress(address);
        const short = `${baseUrl}/j/${compressed}`;
        
        // 二维码数据
        const qr = short; // 使用短链接生成二维码
        
        return { full, short, qr };
    }
    
    /**
     * 地址压缩（链下处理）
     */
    private compressAddress(address: string): string {
        // 简单的Base58实现
        const bs58 = require('bs58');
        const bytes = ethers.getBytes(address);
        return bs58.encode(bytes);
    }
    
    /**
     * 地址解压
     */
    decompressAddress(compressed: string): string {
        try {
            const bs58 = require('bs58');
            const bytes = bs58.decode(compressed);
            return ethers.hexlify(bytes);
        } catch {
            return null;
        }
    }
    
    /**
     * 监听注册事件
     */
    watchRegistrations(callback: (event: any) => void) {
        this.registry.on('UserRegistered', callback);
    }
    
    /**
     * 获取邀请链统计
     */
    async getInvitationStats(address: string): Promise<{
        directInvites: number;
        indirectInvites: number;
        chain: string[];
    }> {
        // 获取邀请链
        const chain = await this.registry.getInvitationChain(address);
        
        // 获取直接邀请数
        const vault = await this.registry.userVaults(address);
        let directInvites = 0;
        
        if (vault !== ethers.ZeroAddress) {
            const vaultContract = new ethers.Contract(vault, VAULT_ABI, this.provider);
            directInvites = await vaultContract.totalInvites();
        }
        
        // 计算间接邀请数（需要遍历或使用事件）
        // 这里简化处理
        const indirectInvites = 0; // TODO: 实现间接邀请统计
        
        return {
            directInvites,
            indirectInvites,
            chain: chain.filter(addr => addr !== ethers.ZeroAddress)
        };
    }
}

// React Hooks
export function useInvitation(registryAddress: string) {
    const { provider, signer } = useWeb3();
    const sdk = new InvitationSDK(registryAddress, provider);
    
    const [userInfo, setUserInfo] = useState(null);
    const [loading, setLoading] = useState(false);
    
    // 获取用户信息
    const fetchUserInfo = async (address: string) => {
        setLoading(true);
        try {
            const info = await sdk.getUserInfo(address);
            setUserInfo(info);
        } finally {
            setLoading(false);
        }
    };
    
    // 注册
    const register = async (inviter: string) => {
        if (!signer) throw new Error('No signer');
        
        setLoading(true);
        try {
            const result = await sdk.register(signer, inviter);
            if (result.success) {
                await fetchUserInfo(await signer.getAddress());
            }
            return result;
        } finally {
            setLoading(false);
        }
    };
    
    return {
        sdk,
        userInfo,
        loading,
        register,
        fetchUserInfo
    };
}

// ABI常量
const REGISTRY_ABI = [
    "function register(address inviter) returns (address vault)",
    "function userVaults(address) view returns (address)",
    "function inviterOf(address) view returns (address)",
    "function batchQuery(address[]) view returns (address[], address[], bool[])",
    "function getInvitationChain(address) view returns (address[])",
    "function computeVaultAddress(address) view returns (address)",
    "function rootUser() view returns (address)",
    "event UserRegistered(address indexed user, address indexed inviter, address indexed vault, uint256 timestamp)"
];

const VAULT_ABI = [
    "function totalInvites() view returns (uint256)",
    "function getInvitedUsers() view returns (address[])",
    "function inviteTimestamps(address) view returns (uint256)"
];