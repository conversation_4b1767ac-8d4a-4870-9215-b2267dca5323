#!/usr/bin/env ts-node

import { Network } from 'alchemy-sdk';
import { AlchemyInvitationQueryService, createAlchemyQueryService } from './alchemy-query-service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 邀请系统数据库构建脚本
 * 从区块链事件重建完整的链下数据库
 */

interface DatabaseConfig {
    alchemyApiKey: string;
    registryAddress: string;
    vaultImplementationAddress: string;
    network: Network;
    startBlock: number;
    outputDir: string;
}

class InvitationDatabaseBuilder {
    private config: DatabaseConfig;
    private service: AlchemyInvitationQueryService | null = null;

    constructor(config: DatabaseConfig) {
        this.config = config;
    }

    /**
     * 构建完整数据库
     */
    async buildDatabase(): Promise<void> {
        console.log('🚀 开始构建邀请系统数据库...');
        console.log(`📍 Registry地址: ${this.config.registryAddress}`);
        console.log(`🔗 网络: ${this.config.network}`);
        console.log(`📦 起始区块: ${this.config.startBlock}`);

        // 创建查询服务
        this.service = await createAlchemyQueryService(
            this.config.alchemyApiKey,
            this.config.registryAddress,
            this.config.vaultImplementationAddress,
            this.config.network
        );

        // 生成各种报告
        await this.generateReports();

        console.log('✅ 数据库构建完成！');
    }

    /**
     * 生成各种数据报告
     */
    private async generateReports(): Promise<void> {
        if (!this.service) throw new Error('Service not initialized');

        console.log('📊 正在生成数据报告...');

        // 1. 导出完整数据库
        await this.exportFullDatabase();

        // 2. 生成系统统计报告
        await this.generateSystemStatsReport();

        // 3. 生成每日统计报告
        await this.generateDailyStatsReport();

        // 4. 生成Top邀请者报告
        await this.generateTopInvitersReport();

        // 5. 生成邀请链分析报告
        await this.generateInvitationChainReport();

        console.log('📋 所有报告生成完成！');
    }

    /**
     * 导出完整数据库
     */
    private async exportFullDatabase(): Promise<void> {
        if (!this.service) return;

        const databaseJson = this.service.exportDatabase();
        const filePath = path.join(this.config.outputDir, 'invitation-database.json');
        
        fs.writeFileSync(filePath, databaseJson);
        console.log(`💾 完整数据库已导出: ${filePath}`);
    }

    /**
     * 生成系统统计报告
     */
    private async generateSystemStatsReport(): Promise<void> {
        if (!this.service) return;

        const stats = await this.service.getSystemStats();
        const report = {
            generatedAt: new Date().toISOString(),
            systemStats: stats,
            summary: {
                totalUsers: stats.totalUsers,
                totalInvitations: stats.totalInvitations,
                activeInviters: stats.activeInviters,
                averageInvitesPerUser: Math.round(stats.averageInvitesPerUser * 100) / 100,
                invitationRate: Math.round((stats.activeInviters / stats.totalUsers) * 100 * 100) / 100 + '%'
            }
        };

        const filePath = path.join(this.config.outputDir, 'system-stats.json');
        fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
        console.log(`📈 系统统计报告已生成: ${filePath}`);
    }

    /**
     * 生成每日统计报告
     */
    private async generateDailyStatsReport(): Promise<void> {
        if (!this.service) return;

        const dailyStats = await this.service.getDailyStats(30);
        const report = {
            generatedAt: new Date().toISOString(),
            period: '30 days',
            dailyStats,
            summary: {
                totalDays: dailyStats.length,
                totalNewRegistrations: dailyStats.reduce((sum, day) => sum + day.newRegistrations, 0),
                averageRegistrationsPerDay: Math.round(
                    dailyStats.reduce((sum, day) => sum + day.newRegistrations, 0) / dailyStats.length * 100
                ) / 100,
                peakRegistrationDay: dailyStats.reduce((max, day) => 
                    day.newRegistrations > max.newRegistrations ? day : max
                )
            }
        };

        const filePath = path.join(this.config.outputDir, 'daily-stats.json');
        fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
        console.log(`📅 每日统计报告已生成: ${filePath}`);
    }

    /**
     * 生成Top邀请者报告
     */
    private async generateTopInvitersReport(): Promise<void> {
        if (!this.service) return;

        const systemStats = await this.service.getSystemStats();
        const topInviters = systemStats.topInviters;

        // 获取详细信息
        const detailedTopInviters = [];
        for (const inviter of topInviters) {
            const userInfo = await this.service.getUserInfo(inviter.address);
            const invitedUsers = await this.service.getInvitedUsersDetailed(inviter.address);
            
            detailedTopInviters.push({
                address: inviter.address,
                totalInvites: inviter.totalInvites,
                registrationTimestamp: userInfo?.registrationTimestamp,
                invitedUsers: invitedUsers.length,
                invitedUsersDetails: invitedUsers.slice(0, 5), // 只显示前5个
                invitationChain: userInfo?.invitationChain || []
            });
        }

        const report = {
            generatedAt: new Date().toISOString(),
            topInviters: detailedTopInviters,
            summary: {
                totalTopInviters: detailedTopInviters.length,
                totalInvitationsByTop: detailedTopInviters.reduce((sum, user) => sum + user.totalInvites, 0),
                averageInvitesPerTopInviter: Math.round(
                    detailedTopInviters.reduce((sum, user) => sum + user.totalInvites, 0) / 
                    detailedTopInviters.length * 100
                ) / 100
            }
        };

        const filePath = path.join(this.config.outputDir, 'top-inviters.json');
        fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
        console.log(`🏆 Top邀请者报告已生成: ${filePath}`);
    }

    /**
     * 生成邀请链分析报告
     */
    private async generateInvitationChainReport(): Promise<void> {
        if (!this.service) return;

        const systemStats = await this.service.getSystemStats();
        const sampleUsers = systemStats.topInviters.slice(0, 10); // 分析前10个邀请者

        const chainAnalysis = [];
        for (const user of sampleUsers) {
            const chainDetails = await this.service.getInvitationChainDetailed(user.address);
            chainAnalysis.push({
                userAddress: user.address,
                totalInvites: user.totalInvites,
                chainLength: chainDetails.length,
                chainDetails: chainDetails
            });
        }

        const report = {
            generatedAt: new Date().toISOString(),
            sampleSize: chainAnalysis.length,
            chainAnalysis,
            summary: {
                averageChainLength: Math.round(
                    chainAnalysis.reduce((sum, chain) => sum + chain.chainLength, 0) / 
                    chainAnalysis.length * 100
                ) / 100,
                longestChain: chainAnalysis.reduce((max, chain) => 
                    chain.chainLength > max.chainLength ? chain : max
                ),
                shortestChain: chainAnalysis.reduce((min, chain) => 
                    chain.chainLength < min.chainLength ? chain : min
                )
            }
        };

        const filePath = path.join(this.config.outputDir, 'invitation-chains.json');
        fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
        console.log(`🔗 邀请链分析报告已生成: ${filePath}`);
    }

    /**
     * 增量更新数据库
     */
    async updateDatabase(fromBlock: number): Promise<void> {
        if (!this.service) {
            this.service = await createAlchemyQueryService(
                this.config.alchemyApiKey,
                this.config.registryAddress,
                this.config.vaultImplementationAddress,
                this.config.network
            );
        }

        console.log(`🔄 正在增量更新数据库，从区块 ${fromBlock} 开始...`);
        await this.service.updateDatabase(fromBlock);
        
        // 重新生成报告
        await this.generateReports();
        
        console.log('✅ 数据库增量更新完成！');
    }
}

/**
 * 主函数
 */
async function main() {
    // 配置参数
    const config: DatabaseConfig = {
        alchemyApiKey: process.env.ALCHEMY_API_KEY || '',
        registryAddress: process.env.REGISTRY_ADDRESS || '',
        vaultImplementationAddress: process.env.VAULT_IMPLEMENTATION_ADDRESS || '',
        network: Network.ETH_MAINNET, // 或者根据需要修改
        startBlock: parseInt(process.env.START_BLOCK || '0'),
        outputDir: process.env.OUTPUT_DIR || './database-reports'
    };

    // 验证配置
    if (!config.alchemyApiKey || !config.registryAddress || !config.vaultImplementationAddress) {
        console.error('❌ 请设置必要的环境变量:');
        console.error('   ALCHEMY_API_KEY');
        console.error('   REGISTRY_ADDRESS');
        console.error('   VAULT_IMPLEMENTATION_ADDRESS');
        process.exit(1);
    }

    // 创建输出目录
    if (!fs.existsSync(config.outputDir)) {
        fs.mkdirSync(config.outputDir, { recursive: true });
    }

    const builder = new InvitationDatabaseBuilder(config);

    try {
        // 检查命令行参数
        const args = process.argv.slice(2);
        if (args.includes('--update')) {
            const fromBlock = parseInt(args[args.indexOf('--update') + 1] || '0');
            await builder.updateDatabase(fromBlock);
        } else {
            await builder.buildDatabase();
        }
    } catch (error) {
        console.error('❌ 构建数据库时发生错误:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

export { InvitationDatabaseBuilder, DatabaseConfig };
