import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// MatchPurchase合约ABI (简化版)
const MATCH_PURCHASE_ABI = [
  "function purchaseMatches(uint256 count) external payable",
  "function registerAndPurchase(address inviter, uint256 count) external payable",
  "function calculateTotalCost(address user, uint256 count) external view returns (uint256)",
  "function getUserDailyPurchases(address user) external view returns (uint256)",
  "function getNextPurchasePrice(address user) external view returns (uint256)",
  "function registry() external view returns (address)",
  "event MatchPurchased(address indexed user, uint256 indexed day, uint256 purchaseCount, uint256 totalCost, uint256 timestamp)",
  "event UserRegisteredAndPurchased(address indexed user, address indexed inviter, address indexed vault, uint256 purchaseCount, uint256 totalCost, uint256 timestamp)"
];

const REGISTRY_ABI = [
  "function isRegistered(address user) external view returns (bool)"
];

interface MatchPurchaseComponentProps {
  matchPurchaseAddress: string;
  provider: ethers.providers.Web3Provider;
  userAddress: string;
}

export const MatchPurchaseComponent: React.FC<MatchPurchaseComponentProps> = ({
  matchPurchaseAddress,
  provider,
  userAddress
}) => {
  const [isRegistered, setIsRegistered] = useState<boolean>(false);
  const [dailyPurchases, setDailyPurchases] = useState<number>(0);
  const [nextPrice, setNextPrice] = useState<string>('0');
  const [purchaseCount, setPurchaseCount] = useState<number>(1);
  const [totalCost, setTotalCost] = useState<string>('0');
  const [inviterAddress, setInviterAddress] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [txHash, setTxHash] = useState<string>('');

  const matchPurchaseContract = new ethers.Contract(
    matchPurchaseAddress,
    MATCH_PURCHASE_ABI,
    provider.getSigner()
  );

  // 检查用户注册状态和购买信息
  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        // 获取Registry合约地址
        const registryAddress = await matchPurchaseContract.registry();
        const registryContract = new ethers.Contract(
          registryAddress,
          REGISTRY_ABI,
          provider
        );

        // 检查用户是否已注册
        const registered = await registryContract.isRegistered(userAddress);
        setIsRegistered(registered);

        // 获取用户当日购买次数
        const purchases = await matchPurchaseContract.getUserDailyPurchases(userAddress);
        setDailyPurchases(purchases.toNumber());

        // 获取下一次购买价格
        const price = await matchPurchaseContract.getNextPurchasePrice(userAddress);
        setNextPrice(ethers.utils.formatEther(price));

      } catch (error) {
        console.error('Error checking user status:', error);
      }
    };

    if (userAddress && matchPurchaseAddress) {
      checkUserStatus();
    }
  }, [userAddress, matchPurchaseAddress, provider]);

  // 计算总费用
  useEffect(() => {
    const calculateCost = async () => {
      try {
        const cost = await matchPurchaseContract.calculateTotalCost(userAddress, purchaseCount);
        let totalCostBN = cost;

        // 如果用户未注册，需要加上注册费用
        if (!isRegistered) {
          const registrationFee = ethers.utils.parseEther('0.01'); // 0.01 MON
          totalCostBN = cost.add(registrationFee);
        }

        setTotalCost(ethers.utils.formatEther(totalCostBN));
      } catch (error) {
        console.error('Error calculating cost:', error);
        setTotalCost('0');
      }
    };

    if (userAddress && purchaseCount > 0) {
      calculateCost();
    }
  }, [userAddress, purchaseCount, isRegistered]);

  // 购买match次数
  const handlePurchase = async () => {
    if (!userAddress || purchaseCount <= 0) return;

    setLoading(true);
    setTxHash('');

    try {
      let tx;
      const value = ethers.utils.parseEther(totalCost);

      if (isRegistered) {
        // 用户已注册，直接购买
        tx = await matchPurchaseContract.purchaseMatches(purchaseCount, { value });
      } else {
        // 用户未注册，需要邀请人地址
        if (!inviterAddress || !ethers.utils.isAddress(inviterAddress)) {
          alert('请输入有效的邀请人地址');
          setLoading(false);
          return;
        }
        tx = await matchPurchaseContract.registerAndPurchase(inviterAddress, purchaseCount, { value });
      }

      setTxHash(tx.hash);
      
      // 等待交易确认
      await tx.wait();
      
      // 刷新用户状态
      window.location.reload();
      
    } catch (error) {
      console.error('Purchase failed:', error);
      alert('购买失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="match-purchase-container" style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <h3>购买Match次数</h3>
      
      {/* 用户状态信息 */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <p><strong>注册状态:</strong> {isRegistered ? '已注册' : '未注册'}</p>
        <p><strong>今日已购买:</strong> {dailyPurchases} 次</p>
        <p><strong>下次购买价格:</strong> {nextPrice} MON</p>
      </div>

      {/* 购买表单 */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '10px' }}>
          购买次数:
          <input
            type="number"
            min="1"
            max="10"
            value={purchaseCount}
            onChange={(e) => setPurchaseCount(parseInt(e.target.value) || 1)}
            style={{ marginLeft: '10px', padding: '5px' }}
          />
        </label>

        {!isRegistered && (
          <label style={{ display: 'block', marginBottom: '10px' }}>
            邀请人地址:
            <input
              type="text"
              placeholder="0x..."
              value={inviterAddress}
              onChange={(e) => setInviterAddress(e.target.value)}
              style={{ marginLeft: '10px', padding: '5px', width: '300px' }}
            />
          </label>
        )}

        <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#e8f4fd', borderRadius: '4px' }}>
          <p><strong>总费用:</strong> {totalCost} MON</p>
          {!isRegistered && <p><small>包含注册费用: 0.01 MON</small></p>}
        </div>
      </div>

      {/* 购买按钮 */}
      <button
        onClick={handlePurchase}
        disabled={loading || (!isRegistered && !inviterAddress)}
        style={{
          padding: '10px 20px',
          backgroundColor: loading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer'
        }}
      >
        {loading ? '处理中...' : (isRegistered ? '购买Match次数' : '注册并购买')}
      </button>

      {/* 交易哈希 */}
      {txHash && (
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#d4edda', borderRadius: '4px' }}>
          <p><strong>交易哈希:</strong></p>
          <a
            href={`https://testnet-explorer.monad.xyz/tx/${txHash}`}
            target="_blank"
            rel="noopener noreferrer"
            style={{ wordBreak: 'break-all', color: '#007bff' }}
          >
            {txHash}
          </a>
        </div>
      )}

      {/* 价格说明 */}
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <h4>价格说明:</h4>
        <ul>
          <li>基础价格: 0.08 MON (每天第一次购买)</li>
          <li>递增规则: 每次购买价格为前一次的108%</li>
          <li>每日重置: 每天重新从基础价格开始</li>
          <li>注册费用: 0.01 MON (仅未注册用户)</li>
        </ul>
      </div>
    </div>
  );
};

export default MatchPurchaseComponent;
