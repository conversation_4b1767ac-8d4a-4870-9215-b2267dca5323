import React, { useState, useEffect } from 'react';
import { useInvitation } from '../scripts/invitation-sdk';
import { QRCodeSVG } from 'qrcode.react';
import { toast } from 'react-toastify';

/**
 * 邀请组件 - 展示分享链接和二维码
 */
export function InviteComponent({ registryAddress }: { registryAddress: string }) {
    const { account, signer } = useWeb3();
    const { sdk, userInfo, loading, fetchUserInfo } = useInvitation(registryAddress);
    
    const [inviteLinks, setInviteLinks] = useState(null);
    const [copied, setCopied] = useState(false);
    
    useEffect(() => {
        if (account && userInfo) {
            const links = sdk.generateInviteLink(account);
            setInviteLinks(links);
        }
    }, [account, userInfo, sdk]);
    
    const copyToClipboard = async (text: string) => {
        await navigator.clipboard.writeText(text);
        setCopied(true);
        toast.success('链接已复制');
        setTimeout(() => setCopied(false), 2000);
    };
    
    if (loading) return <div>Loading...</div>;
    
    if (!userInfo) {
        return (
            <div className="text-center p-8">
                <p>您还未注册，请通过邀请链接加入</p>
            </div>
        );
    }
    
    return (
        <div className="invite-container p-6 bg-white rounded-lg shadow">
            <h2 className="text-2xl font-bold mb-4">邀请好友</h2>
            
            {/* 邀请统计 */}
            <div className="stats grid grid-cols-2 gap-4 mb-6">
                <div className="stat">
                    <p className="text-gray-500">直接邀请</p>
                    <p className="text-3xl font-bold">{userInfo.totalInvites}</p>
                </div>
                <div className="stat">
                    <p className="text-gray-500">团队总人数</p>
                    <p className="text-3xl font-bold">-</p>
                </div>
            </div>
            
            {/* 邀请链接 */}
            <div className="links space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-1">完整链接</label>
                    <div className="flex">
                        <input
                            type="text"
                            value={inviteLinks?.full || ''}
                            readOnly
                            className="flex-1 p-2 border rounded-l"
                        />
                        <button
                            onClick={() => copyToClipboard(inviteLinks?.full)}
                            className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600"
                        >
                            {copied ? '已复制' : '复制'}
                        </button>
                    </div>
                </div>
                
                <div>
                    <label className="block text-sm font-medium mb-1">短链接</label>
                    <div className="flex">
                        <input
                            type="text"
                            value={inviteLinks?.short || ''}
                            readOnly
                            className="flex-1 p-2 border rounded-l"
                        />
                        <button
                            onClick={() => copyToClipboard(inviteLinks?.short)}
                            className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600"
                        >
                            复制
                        </button>
                    </div>
                </div>
            </div>
            
            {/* 二维码 */}
            <div className="qr-section mt-6">
                <h3 className="text-lg font-medium mb-2">二维码分享</h3>
                <div className="bg-gray-50 p-4 rounded inline-block">
                    <QRCodeSVG
                        value={inviteLinks?.short || ''}
                        size={200}
                        level="L"
                    />
                </div>
            </div>
            
            {/* 分享按钮 */}
            <div className="share-buttons mt-6 flex gap-2">
                <ShareButton
                    platform="twitter"
                    url={inviteLinks?.short}
                    text="加入我们的社区！"
                />
                <ShareButton
                    platform="telegram"
                    url={inviteLinks?.short}
                    text="加入我们的社区！"
                />
                <ShareButton
                    platform="copy"
                    url={inviteLinks?.short}
                    onShare={() => copyToClipboard(inviteLinks?.short)}
                />
            </div>
        </div>
    );
}

/**
 * 接受邀请组件
 */
export function JoinComponent({ registryAddress }: { registryAddress: string }) {
    const { account } = useWeb3();
    const { sdk, register, loading } = useInvitation(registryAddress);
    
    const [inviter, setInviter] = useState('');
    const [verifying, setVerifying] = useState(false);
    const [error, setError] = useState('');
    
    // 从URL获取邀请人
    useEffect(() => {
        const params = new URLSearchParams(window.location.search);
        const inviterParam = params.get('inviter') || params.get('i');
        
        if (inviterParam) {
            // 尝试解压地址
            const decompressed = sdk.decompressAddress(inviterParam);
            setInviter(decompressed || inviterParam);
        }
    }, [sdk]);
    
    const handleVerify = async () => {
        setVerifying(true);
        setError('');
        
        try {
            const result = await sdk.verifyInvitation(inviter, account);
            if (!result.valid) {
                setError(result.reason);
            }
        } catch (err) {
            setError('验证失败');
        } finally {
            setVerifying(false);
        }
    };
    
    const handleJoin = async () => {
        try {
            const result = await register(inviter);
            if (result.success) {
                toast.success('注册成功！');
                // 跳转到主页面
                window.location.href = '/dashboard';
            } else {
                setError(result.error);
            }
        } catch (err) {
            setError('注册失败');
        }
    };
    
    return (
        <div className="join-container max-w-md mx-auto p-6">
            <h2 className="text-2xl font-bold mb-6">加入社区</h2>
            
            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-1">
                        邀请人地址
                    </label>
                    <input
                        type="text"
                        value={inviter}
                        onChange={(e) => setInviter(e.target.value)}
                        placeholder="0x..."
                        className="w-full p-2 border rounded"
                    />
                </div>
                
                {error && (
                    <div className="text-red-500 text-sm">
                        {error}
                    </div>
                )}
                
                <div className="flex gap-2">
                    <button
                        onClick={handleVerify}
                        disabled={!inviter || verifying}
                        className="flex-1 py-2 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
                    >
                        {verifying ? '验证中...' : '验证邀请码'}
                    </button>
                    
                    <button
                        onClick={handleJoin}
                        disabled={!inviter || loading || !!error}
                        className="flex-1 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                    >
                        {loading ? '注册中...' : '立即加入'}
                    </button>
                </div>
            </div>
            
            <div className="mt-8 text-sm text-gray-600">
                <p>注册需要支付 Gas 费用（约 $10-15）</p>
                <p>建议在 Layer 2 网络上操作以降低成本</p>
            </div>
        </div>
    );
}

/**
 * 分享按钮组件
 */
function ShareButton({ 
    platform, 
    url, 
    text,
    onShare 
}: { 
    platform: string;
    url: string;
    text?: string;
    onShare?: () => void;
}) {
    const handleShare = () => {
        if (onShare) {
            onShare();
            return;
        }
        
        let shareUrl = '';
        switch (platform) {
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
                break;
            case 'telegram':
                shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank');
        }
    };
    
    return (
        <button
            onClick={handleShare}
            className="px-4 py-2 border rounded hover:bg-gray-50"
        >
            {platform}
        </button>
    );
}